//
//  CalendarDataSource.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import Foundation

class CalendarDataSource {
    static let shared = CalendarDataSource()
    private var currentEventRequestId: UUID = UUID()
    var events: [CalendarEventModel] = [] {
        didSet {
            // 当事件更新时，重新按日期分组
            updateEventsByDate()
        }
    }
    private(set) var eventsByDate: [String: [CalendarEventModel]] = [:]
    private(set) var availableWords: [String] = []
    // 日历标记数据
    private(set) var calendarMarks: [[String: Any]] = []
    // 是否只显示重要事件
    var filterImportantOnly: Bool = false

    var selectedFilters: [EventTypeFilter] = [.all]
    var selectedDate: Date = Date()

    // 回调
    var onWordsLoaded: (([String]) -> Void)?

    private init() {}

    /// 加载事件分类words
    func loadEventWords(completion: @escaping (Bool) -> Void) {
        CalendarEventManager.shared.getEventWords { [weak self] words, error in
            DispatchQueue.main.async {
                guard let self = self else { return }

                if let words = words {
                    self.availableWords = words
                    self.onWordsLoaded?(words)
                    completion(true)
                } else {
                    // 使用默认words
                    let defaultWords = ["ETF", "宏观数据", "代币解锁", "上新下架"]
                    self.availableWords = defaultWords
                    self.onWordsLoaded?(defaultWords)
                    completion(false)
                }
            }
        }
    }

    /// 从words创建过滤器列表
    func createFiltersFromWords() -> [EventTypeFilter] {
        return EventTypeFilter.createFilters(from: availableWords)
    }

    func loadEvents(date: Date, filters: [EventTypeFilter], completion: @escaping (Bool) -> Void) {

        let requestId = UUID()
        currentEventRequestId = requestId
        // 根据过滤器构造words参数
        var words: [String] = []

        // 如果不是"全部"过滤器，则直接使用过滤器的title作为words
        if !filters.contains(where: { $0.id == "all" }) {
            words = filters.map { $0.title }
        }

        let request = CalendarEventRequestModel(date: date, words: words)

        CalendarEventManager.shared.getDayEvents(request: request) { [weak self] (events, error) in
            DispatchQueue.main.async {
                guard let self = self else { return }
                // 验证这是否是最新请求的响应，解决使用缓存和网络请求之间的竞争冲突
                guard requestId == self.currentEventRequestId else {
                    print("忽略过时的响应，请求ID: \(requestId)")
                    return  // 忽略过时的响应
                }
                if let events = events {
                    self.events = events
                    completion(true)
                } else {
                    // 网络请求失败，记录错误但不清空现有数据
                    if let error = error {
                        print("加载日历事件失败: \(error.localizedDescription)")
                    }
                    completion(false)
                }
            }
        }
    }

    func filteredEventsForSelectedDate() -> [CalendarEventModel] {
        let dateKey = formatDateToString(date: selectedDate)
        let events = eventsByDate[dateKey] ?? []

        // 过滤重要事件
        let filtered: [CalendarEventModel]
        if filterImportantOnly {
            filtered = events.filter { $0.computedIsImportant }
        } else {
            filtered = events
        }

        // 未开始的在后，已开始的在前，时间前的排在前面
        return filtered.sorted {
            !$0.isPending && $1.isPending || ($0.isPending == $1.isPending && $0.date < $1.date)
        }
    }

    /// 加载日历标记数据（初始加载三个月）
    func loadCalendarMarks(forMonth date: Date, completion: @escaping (Bool) -> Void) {
        print("loadCalendarMarks forMonth: \(date)")
        // 计算时间范围：前一个月到后一个月
        let calendar = Calendar.current

        // 获取当前月的第一天
        let monthComponents = calendar.dateComponents([.year, .month], from: date)
        let monthStart = calendar.date(from: monthComponents)!

        // 获取前一个月的第一天
        let prevMonthComponents = calendar.dateComponents(
            [.year, .month], from: calendar.date(byAdding: .month, value: -1, to: monthStart)!)
        let prevMonthStart = calendar.date(from: prevMonthComponents)!

        // 获取后一个月的最后一天
        let nextMonthStart = calendar.date(byAdding: .month, value: 1, to: monthStart)!
        let afterNextMonthStart = calendar.date(byAdding: .month, value: 1, to: nextMonthStart)!
        let nextMonthEnd = calendar.date(byAdding: .day, value: -1, to: afterNextMonthStart)!

        // 转换为毫秒时间戳
        let startTime = calendar.startOfDay(for: prevMonthStart).timeIntervalSince1970 * 1000
        let endTime =
            calendar.startOfDay(for: nextMonthEnd).timeIntervalSince1970 * 1000 + 24 * 60 * 60
            * 1000 - 1

        CalendarEventManager.shared.getCalendarMarks(startTime: startTime, endTime: endTime) {
            [weak self] (marks, error) in
            DispatchQueue.main.async {
                guard let self = self else { return }

                if let marks = marks {
                    self.calendarMarks = marks
                    completion(true)
                } else {
                    // 如果获取失败，清空现有标记
                    self.calendarMarks = []
                    completion(false)
                }
            }
        }
    }

    /// 确保指定月份的标记数据已加载
    func ensureMarksLoaded(forMonth date: Date, completion: @escaping (Bool) -> Void) {
        print("ensureMarksLoaded forMonth: \(date)")
        // 如果没有标记数据，执行初始加载
        if calendarMarks.isEmpty {
            loadCalendarMarks(forMonth: date, completion: completion)
            return
        }

        let calendar = Calendar.current

        // 计算目标3个月范围（前月+当月+后月）
        let monthComponents = calendar.dateComponents([.year, .month], from: date)
        let monthStart = calendar.date(from: monthComponents)!
        let prevMonthStart = calendar.date(byAdding: .month, value: -1, to: monthStart)!
        let nextMonthStart = calendar.date(byAdding: .month, value: 1, to: monthStart)!
        let afterNextMonthStart = calendar.date(byAdding: .month, value: 1, to: nextMonthStart)!
        let nextMonthEnd = calendar.date(byAdding: .day, value: -1, to: afterNextMonthStart)!

        // 转换为毫秒时间戳
        let targetStartTime = calendar.startOfDay(for: prevMonthStart).timeIntervalSince1970 * 1000
        let targetEndTime =
            calendar.startOfDay(for: nextMonthEnd).timeIntervalSince1970 * 1000 + 24 * 60 * 60
            * 1000 - 1

        // 获取当前缓存的时间范围
        let timeValues = calendarMarks.compactMap { $0["time"] as? TimeInterval }

        // 确保有缓存数据
        guard !timeValues.isEmpty else {
            loadCalendarMarks(forMonth: date, completion: completion)
            return
        }

        // 按日期排序并提取时间戳范围（确保单位一致性）
        let minTime = timeValues.min() ?? TimeInterval.greatestFiniteMagnitude
        let maxTime = timeValues.max() ?? 0

        // 计算缺失的范围
        var missingRanges: [(start: TimeInterval, end: TimeInterval)] = []

        // 检查前半部分是否缺失
        if targetStartTime < minTime {
            missingRanges.append((targetStartTime, minTime - 1))
        }

        // 检查后半部分是否缺失
        if targetEndTime > maxTime {
            missingRanges.append((maxTime + 1, targetEndTime))
        }

        // 如果没有缺失范围，直接返回成功
        if missingRanges.isEmpty {
            completion(true)
            return
        }

        // 使用DispatchGroup管理多个请求
        let group = DispatchGroup()
        var allSuccess = true

        for range in missingRanges {
            group.enter()
            CalendarEventManager.shared.getCalendarMarks(startTime: range.start, endTime: range.end)
            { [weak self] (marks, error) in
                DispatchQueue.main.async {
                    guard let self = self else {
                        group.leave()
                        return
                    }

                    if let marks = marks, !marks.isEmpty {
                        // 合并新的标记数据
                        self.calendarMarks.append(contentsOf: marks)
                    } else {
                        allSuccess = false
                    }
                    group.leave()
                }
            }
        }

        group.notify(queue: .main) {
            completion(allSuccess)
        }
    }

    func hasEvents(for date: Date) -> Bool {
        // 如果有标记数据，优先使用标记数据
        if !calendarMarks.isEmpty {
            let calendar = Calendar.current

            // 查找当天score >= 3的标记
            return calendarMarks.contains { mark in
                guard let time = mark["time"] as? TimeInterval,
                    let score = mark["score"] as? Int
                else { return false }

                let markDate = Date(timeIntervalSince1970: time / 1000.0)
                return calendar.isDate(markDate, inSameDayAs: date) && score >= 3
            }
        }
        return false
    }

    func hasImportantEvents(for date: Date) -> Bool {
        // 如果有标记数据，优先使用标记数据
        if !calendarMarks.isEmpty {
            let calendar = Calendar.current

            // 查找当天的重要标记
            return calendarMarks.contains { mark in
                guard let time = mark["time"] as? TimeInterval,
                    let score = mark["score"] as? Int
                else { return false }

                let markDate = Date(timeIntervalSince1970: time / 1000.0)
                return calendar.isDate(markDate, inSameDayAs: date) && score >= 3
            }
        }
        return false
    }

    /// 检查指定日期的重要事件是否已过期
    /// - Parameter date: 要检查的日期
    /// - Returns: true表示已过期（事件时间在今天之后），false表示未过期
    func hasExpiredImportantEvents(for date: Date) -> Bool {
        // 如果有标记数据，优先使用标记数据
        if !calendarMarks.isEmpty {
            let calendar = Calendar.current
            let today = Date()

            // 查找当天的重要标记，并检查是否已过期
            return calendarMarks.contains { mark in
                guard let time = mark["time"] as? TimeInterval,
                    let score = mark["score"] as? Int
                else { return false }

                let markDate = Date(timeIntervalSince1970: time / 1000.0)
                let isOnTargetDate = calendar.isDate(markDate, inSameDayAs: date)
                let isImportant = score >= 3
                let isExpired = markDate < today && !calendar.isDate(markDate, inSameDayAs: today)

                return isOnTargetDate && isImportant && isExpired
            }
        }
        return false
    }

    private func formatDateToString(date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }

    // 更新按日期分组的事件
    private func updateEventsByDate() {
        // 清空现有的分组
        eventsByDate.removeAll()

        // 重新按日期分组事件
        for event in events {
            let dateKey = formatDateToString(date: event.date)
            if eventsByDate[dateKey] == nil {
                eventsByDate[dateKey] = []
            }
            eventsByDate[dateKey]?.append(event)
        }
    }

    /// 更新特定事件的状态
    func updateEvent(_ updatedEvent: CalendarEventModel) {
        // 在events数组中查找并更新对应的事件
        for i in 0..<events.count {
            if events[i].id == updatedEvent.id {
                events[i] = updatedEvent
                break
            }
        }
        // events的didSet会自动调用updateEventsByDate()
    }

    /// 清除页面缓存（页面退出时调用）
    func clearCache() {
        CalendarEventManager.shared.clearSessionCache()
    }
}
