//
//  CalendarEventReminderViewController.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-19.
//  Copyright © 2024 AICoin. All rights reserved.
//

import SnapKit
import UIKit

/// 日历事件提醒管理界面
class CalendarEventReminderViewController: AICBaseViewController, WMPageControllerDelegate,
    WMPageControllerDataSource
{

    // MARK: - 枚举

    /// 提醒类型
    enum ReminderType: Int, CaseIterable {
        case eventReminder = 0  // 事件提醒
        case historyReminder = 1  // 历史提醒

        var title: String {
            switch self {
            case .eventReminder:
                return "事件提醒".base.localized
            case .historyReminder:
                return "历史提醒".base.localized
            }
        }
    }

    // MARK: - 属性

    /// 分页控制器
    private var pageController: HomePageBasePageViewController!

    /// 提醒类型数组
    private let reminderTypes: [ReminderType] = ReminderType.allCases

    /// 子视图控制器数组
    private var reminderViewControllers: [CalendarEventReminderListViewController] = []

    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()

        setupNavigation()
        setupChildViewControllers()
        setupViews()
    }

    // MARK: - 设置

    private func setupNavigation() {
        title = "事件提醒".base.localized
    }

    private func setupViews() {
        view.backgroundColor = UIColor.baseTheme.current.bgColor

        // 创建并配置分页控制器
        pageController = HomePageBasePageViewController()
        pageController.delegate = self
        pageController.dataSource = self
        pageController.selectIndex = 0
        pageController.menuViewLayoutMode = .center
        pageController.itemMargin = 40
        pageController.titleColorSelected = UIColor.baseTheme.current.cellTitleColor
        pageController.titleColorNormal = DynamicHelper.themeColor(day: 0x8C96A3, night: 0x8C96A3)
        pageController.setCustomWithNormalFont(
            UIFont.systemFont(ofSize: 17),
            selectedFont: UIFont.systemFont(ofSize: 17, weight: .medium))
        pageController.progressColor = UIColor.baseTheme.current.mainColor
        pageController.menuViewBgColor = UIColor.baseTheme.current.bgColor
        pageController.scrollViewBGColor = UIColor.baseTheme.current.bgColor
        pageController.isShowSeparator = false

        addChild(pageController)
        view.addSubview(pageController.view)
        pageController.didMove(toParent: self)

        pageController.view.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func setupChildViewControllers() {
        // 创建子视图控制器
        for type in reminderTypes {
            let listVC = CalendarEventReminderListViewController(reminderType: type)
            reminderViewControllers.append(listVC)
        }
    }

}

// MARK: - WMPageControllerDelegate, WMPageControllerDataSource

extension CalendarEventReminderViewController {

    func numbersOfChildControllers(in pageController: WMPageController) -> Int {
        return reminderTypes.count
    }

    func pageController(_ pageController: WMPageController, titleAt index: Int) -> String {
        return reminderTypes[index].title
    }

    func pageController(_ pageController: WMPageController, viewControllerAt index: Int)
        -> UIViewController
    {
        return reminderViewControllers[index]
    }

    func pageController(_ pageController: WMPageController, preferredFrameFor menuView: WMMenuView)
        -> CGRect
    {
        return CGRect(x: 0, y: 0, width: view.bounds.width, height: 44)
    }

    func pageController(
        _ pageController: WMPageController, preferredFrameForContentView contentView: WMScrollView
    ) -> CGRect {
        return CGRect(x: 0, y: 44, width: view.bounds.width, height: view.bounds.height - 44)
    }

    func pageController(
        _ pageController: WMPageController, didEnter viewController: UIViewController,
        withInfo info: [AnyHashable: Any]
    ) {
        // 页面切换完成后的处理
    }
}

// MARK: - 子视图控制器

/// 事件提醒列表视图控制器
class CalendarEventReminderListViewController: AICBaseViewController {

    // MARK: - 属性

    /// 提醒类型
    private let reminderType: CalendarEventReminderViewController.ReminderType

    /// 表格视图
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.backgroundColor = UIColor.baseTheme.current.bgColor
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(
            CalendarEventReminderCell.self, forCellReuseIdentifier: "CalendarEventReminderCell")
        tableView.register(
            CalendarEventReminderHeaderView.self,
            forHeaderFooterViewReuseIdentifier: "CalendarEventReminderHeaderView")
        // 禁用下拉刷新
        tableView.alwaysBounceVertical = false
        return tableView
    }()

    /// 空视图模型
    private lazy var emptyModel: BaseEmptyDelegateModel = {
        let model = BaseEmptyDelegateModel()
        model.titleString = "暂无记录".base.localized
        model.image = UIImage.ticker.image(name: "暂无内容")
        return model
    }()

    /// 数据源
    private var dataSections: [EventReminderSectionModel] = []

    // MARK: - 初始化

    init(reminderType: CalendarEventReminderViewController.ReminderType) {
        self.reminderType = reminderType
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()

        setupViews()
        setupEmptyView()
        loadMockData()
    }

    // MARK: - 设置

    private func setupViews() {
        view.backgroundColor = UIColor.baseTheme.current.bgColor

        view.addSubview(tableView)

        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func setupEmptyView() {
        tableView.emptyDataSetSource = emptyModel
        tableView.emptyDataSetDelegate = emptyModel
    }

    // MARK: - 数据加载

    private func loadMockData() {
        switch reminderType {
        case .eventReminder:
            // 模拟事件提醒数据
            let eventSection1 = EventReminderSectionModel(
                dateString: "05月18日",
                events: [
                    EventReminderModel(
                        title: "美联储主席鲍威尔在杰克逊霍尔年会上就经济前景发表讲话",
                        reminderTime: "提醒时间：05月15日　22:00"
                    ),
                    EventReminderModel(
                        title: "美联储主席鲍威尔在杰克逊霍尔年会上就经济前景发表讲话",
                        reminderTime: "提醒时间：05月17日　22:00"
                    ),
                ]
            )

            let eventSection2 = EventReminderSectionModel(
                dateString: "05月20日",
                events: [
                    EventReminderModel(
                        title: "美联储主席鲍威尔在杰克逊霍尔年会上就经济前景发表讲话",
                        reminderTime: "提醒时间：05月15日　22:00"
                    ),
                    EventReminderModel(
                        title: "美联储主席鲍威尔在杰克逊霍尔年会上就经济前景发表讲话",
                        reminderTime: "提醒时间：05月17日　22:00"
                    ),
                ]
            )

            dataSections = [eventSection1, eventSection2]

        case .historyReminder:
            // 历史提醒暂时为空
            dataSections = []
        }

        tableView.reloadData()
    }

    // MARK: - 事件处理

    @objc private func editButtonTapped(_ sender: UIButton) {
        // TODO: 实现编辑功能
        print("编辑按钮被点击")
    }

    @objc private func deleteButtonTapped(_ sender: UIButton) {
        // TODO: 实现删除功能
        print("删除按钮被点击")
    }
}

// MARK: - UITableViewDataSource, UITableViewDelegate

extension CalendarEventReminderListViewController: UITableViewDataSource, UITableViewDelegate {

    func numberOfSections(in tableView: UITableView) -> Int {
        return dataSections.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return dataSections[section].events.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell =
            tableView.dequeueReusableCell(
                withIdentifier: "CalendarEventReminderCell", for: indexPath)
            as! CalendarEventReminderCell

        let event = dataSections[indexPath.section].events[indexPath.row]
        cell.configure(with: event)

        // 设置按钮回调
        cell.editButtonTapped = { [weak self] in
            self?.editButtonTapped(UIButton())
        }

        cell.deleteButtonTapped = { [weak self] in
            self?.deleteButtonTapped(UIButton())
        }

        return cell
    }

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView =
            tableView.dequeueReusableHeaderFooterView(
                withIdentifier: "CalendarEventReminderHeaderView")
            as! CalendarEventReminderHeaderView
        let dateString = dataSections[section].dateString
        headerView.configure(with: dateString)
        return headerView
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 50
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath)
        -> CGFloat
    {
        return 80
    }
}

// MARK: - 数据模型

/// 事件提醒分组模型
struct EventReminderSectionModel {
    let dateString: String  // 日期字符串，如 "05月18日"
    let events: [EventReminderModel]  // 该日期下的事件列表
}

/// 事件提醒模型
struct EventReminderModel {
    let title: String  // 事件标题
    let reminderTime: String  // 提醒时间描述
}

// MARK: - 自定义单元格
class CalendarEventReminderCell: UITableViewCell {

    // MARK: - 属性

    /// 编辑按钮点击回调
    var editButtonTapped: (() -> Void)?

    /// 删除按钮点击回调
    var deleteButtonTapped: (() -> Void)?

    /// 事件标题标签
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 15)
        label.textColor = UIColor.baseTheme.current.cellTitleColor
        label.numberOfLines = 0
        return label
    }()

    /// 提醒时间标签
    private let reminderTimeLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .light)
        label.textColor = DynamicHelper.themeColor(day: 0x7A8899, night: 0x7A8899)
        return label
    }()

    /// 编辑按钮
    private lazy var editButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage.base.image(name: "Icon_Ticker_Edit_Synthesis"), for: .normal)
        button.addTarget(self, action: #selector(editButtonAction), for: .touchUpInside)
        return button
    }()

    /// 删除按钮
    private lazy var deleteButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage.base.image(name: "Icon_Ticker_Tab_Delete"), for: .normal)
        button.addTarget(self, action: #selector(deleteButtonAction), for: .touchUpInside)
        return button
    }()

    /// 按钮容器视图
    private let buttonContainerView: UIView = {
        let view = UIView()
        return view
    }()

    /// 容器视图
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.baseTheme.current.cellBgColor
        view.layer.cornerRadius = 12
        return view
    }()

    /// 分隔线
    private let separatorLine: UIView = {
        let view = UIView()
        view.backgroundColor = DynamicHelper.themeColor(day: 0xF0F0F0, night: 0x2A2A2A)
        return view
    }()

    // MARK: - 初始化

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupViews()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 设置UI

    private func setupViews() {
        selectionStyle = .none
        backgroundColor = .clear

        contentView.addSubview(containerView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(reminderTimeLabel)
        containerView.addSubview(buttonContainerView)
        containerView.addSubview(separatorLine)

        buttonContainerView.addSubview(editButton)
        buttonContainerView.addSubview(deleteButton)

        setupConstraints()
    }

    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.top.bottom.equalToSuperview().inset(1)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(24)
            make.left.equalToSuperview().offset(32)
            make.right.equalTo(buttonContainerView.snp.left).offset(-16)
        }

        reminderTimeLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.left.equalTo(titleLabel)
            make.right.equalTo(titleLabel)
            make.bottom.equalToSuperview().offset(-24)
        }

        buttonContainerView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-32)
            make.centerY.equalToSuperview()
            make.width.equalTo(80)
            make.height.equalTo(40)
        }

        editButton.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }

        deleteButton.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }

        separatorLine.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(32)
            make.right.equalToSuperview().offset(-32)
            make.bottom.equalToSuperview()
            make.height.equalTo(1)
        }
    }

    // MARK: - 动作

    @objc private func editButtonAction() {
        editButtonTapped?()
    }

    @objc private func deleteButtonAction() {
        deleteButtonTapped?()
    }

    // MARK: - 公共方法

    func configure(with model: EventReminderModel) {
        titleLabel.text = "·" + model.title
        reminderTimeLabel.text = model.reminderTime
    }
}

// MARK: - 自定义头视图
class CalendarEventReminderHeaderView: UITableViewHeaderFooterView {

    // MARK: - 属性

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 13)
        label.textColor = DynamicHelper.themeColor(day: 0x7A8899, night: 0x7A8899)
        return label
    }()

    // MARK: - 初始化

    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        setupViews()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 设置UI

    private func setupViews() {
        contentView.backgroundColor = UIColor.baseTheme.current.bgColor

        contentView.addSubview(titleLabel)

        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(32)
            make.centerY.equalToSuperview()
        }
    }

    // MARK: - 公共方法

    func configure(with title: String) {
        titleLabel.text = title
    }
}
