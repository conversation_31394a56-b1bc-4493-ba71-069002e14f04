//
//  CalendarViewController.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import SnapKit
import UIKit

class CalendarViewController: AICBaseViewController {

    // MARK: - 属性

    private let viewModel = CalendarViewModel()
    private let foldableCalendarView = CalendarFoldableView()

    // 事件列表相关视图
    private let eventTypeTabView = EventTypeTabView()
    private let eventListView = EventListView()

    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()
        self.title = "日历".base.localized

        setupViews()
        bindViewModel()
    }

    deinit {
        CalendarEventManager.shared.clearSessionCache()
    }

    // MARK: - 设置

    private func setupViews() {
        view.backgroundColor = UIColor.baseTheme.current.bgColor

        // 添加日历视图
        view.addSubview(foldableCalendarView)

        // 添加事件类型选择视图和事件列表视图
        view.addSubview(eventTypeTabView)
        view.addSubview(eventListView)

        // 设置约束
        foldableCalendarView.snp.makeConstraints { make in
            make.top.equalTo(view)
            make.left.right.equalTo(view)
        }

        eventTypeTabView.snp.makeConstraints { make in
            make.top.equalTo(foldableCalendarView.snp.bottom).offset(8)
            make.left.right.equalTo(view)
            make.height.equalTo(35)
        }

        eventListView.snp.makeConstraints { make in
            make.top.equalTo(eventTypeTabView.snp.bottom)
            make.left.right.equalTo(view)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
        }
    }

    private func bindViewModel() {
        // 配置日历视图
        foldableCalendarView.configure(with: viewModel)
        foldableCalendarView.delegate = self

        // 配置事件列表视图
        eventTypeTabView.configure(with: viewModel.availableFilters)
        eventListView.configure(with: viewModel)

        // 设置事件选择回调
        eventListView.onEventSelected = { [weak self] event in
            let eventDetailVC = EventDetailViewController(event: event)

            // 设置提醒设置成功的回调，更新ViewModel中的事件状态
            eventDetailVC.onEventUpdated = { [weak self] updatedEvent in
                self?.viewModel.updateEvent(updatedEvent)
            }

            self?.navigationController?.pushViewController(eventDetailVC, animated: true)
        }

        // 设置事件提醒弹窗点击回调
        eventListView.onEventReminder = { [weak self] event in
            let reminderVC = EventReminderViewController(event: event)

            // 设置提醒设置成功的回调，更新ViewModel中的事件状态
            reminderVC.onReminderSettingSuccess = { [weak self] updatedEvent in
                self?.viewModel.updateEvent(updatedEvent)
            }

            self?.present(reminderVC, animated: true, completion: nil)
        }

        // 设置标签点击回调
        eventListView.onTagTapped = { [weak self] entrance in
            if entrance.entranceType == 1 {
                // K线入口，跳转K线详情页
                UIViewController.aic_getRootNavigationController()?.jumpToTickerDetailVC(
                    firstKey: entrance.key, listKeys: nil)
            } else {
                // 网页入口，跳转H5页面
                UIViewController.aic_getRootNavigationController()?.jumpToWebViewController(
                    entrance.link)
            }
        }
        // 设置事件类型选择回调
        eventTypeTabView.onFilterSelected = { [weak self] filter in
            self?.viewModel.applyFilter(filter)
        }

        // 设置视图模型回调
        viewModel.onFilterChanged = { [weak self] in
            self?.eventTypeTabView.configure(with: self?.viewModel.availableFilters ?? [])
        }

        viewModel.onEventsUpdated = { [weak self] in
            self?.eventListView.reloadData()
        }

        // 首先初始化words，然后再加载事件
        viewModel.initializeEventWords()
    }

}

// MARK: - CalendarFoldableViewDelegate
extension CalendarViewController: CalendarFoldableViewDelegate {

    func calendarFoldableView(_ view: CalendarFoldableView, didChangeMonth date: Date) {
        // 月份变更由ViewModel处理
    }

    func calendarFoldableView(_ view: CalendarFoldableView, didChangeFoldState isCollapsed: Bool) {
        // 调整事件列表的位置
        //        UIView.animate(withDuration: 0.3) {
        //            self.eventTypeTabView.snp.updateConstraints { make in
        //                make.top.equalTo(self.foldableCalendarView.snp.bottom).offset(16)
        //            }
        //            self.view.layoutIfNeeded()
        //        }
    }

    func calendarFoldableView(
        _ view: CalendarFoldableView, didChangeImportantOnly isImportantOnly: Bool
    ) {
        // 在ViewModel中已处理，这里不需要额外处理
    }

    func calendarFoldableViewDidTapEventReminder(_ view: CalendarFoldableView) {
        // 跳转到事件提醒页面
        let reminderVC = CalendarEventReminderViewController()
        reminderVC.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(reminderVC, animated: true)
    }
}
