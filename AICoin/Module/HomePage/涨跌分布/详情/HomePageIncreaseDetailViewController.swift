//
//  HomePageIncreaseDetailViewController.swift
//  AICoin
//
//  Created by <PERSON><PERSON><PERSON> on 2019/1/14.
//  Copyright © 2019 AICoin. All rights reserved.
//

import UIKit
import SnapKit

class HomePageIncreaseDetailViewController: HomePageBaseIndicatorInNavViewController {
    
    private lazy var dataSource: [HomePageConstant.DataType] = {
        var dataSource: [HomePageConstant.DataType] = [.trading, .coin]
        return dataSource
    }()


    override func pageController(_ pageController: WMPageController, preferredFrameForContentView contentView: WMScrollView) -> CGRect {
        let menuFrame = self.menuFrame
        return CGRect(x: 0, y: menuFrame.maxY + 3, width: self.view.width, height: self.view.height - menuFrame.maxY)
    }
    
    override func pageController(_ pageController: WMPageController, preferredFrameFor menuView: WMMenuView) -> CGRect {
        return self.menuFrame
    }
    
    override func numbersOfChildControllers(in pageController: WMPageController) -> Int {
        return self.dataSource.count
    }
    
    override func pageController(_ pageController: WMPageController, viewControllerAt index: Int) -> UIViewController {
        let type = self.dataSource[index]
        let vc = HomePageIncreaseDetailItemViewController(classType: type)
        return vc
    }
    
    override func pageController(_ pageController: WMPageController, titleAt index: Int) -> String {
        let type = self.dataSource[index]
        return type.title.home.localized
    }
}

