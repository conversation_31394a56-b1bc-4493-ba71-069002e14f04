//
//  HomePageBaseIndicatorInNavViewController.swift
//  AICoin
//
//  Created by <PERSON><PERSON><PERSON> on 2019/1/19.
//  Copyright © 2019 AICoin. All rights reserved.
//  指示器在导航栏上的分页控制器

import UIKit

class HomePageBaseIndicatorInNavViewController: AICBaseViewController, WMPageControllerDelegate, WMPageControllerDataSource {
    
    lazy var pageController: HomePageBasePageViewController = {
        var pageController = HomePageBasePageViewController()
        pageController.delegate = self
        pageController.dataSource = self
        pageController.menuViewLayoutMode = .center
        pageController.itemMargin = 55
        let color = UIColor.home.current.inNavPageVCIndicatorColor
        pageController.titleColorSelected = UIColor.baseTheme.current.level2SegmentedSelectedColor
        pageController.titleColorNormal = UIColor.baseTheme.current.level2SegmentedNormalColor
        pageController.setCustomWithNormalFont(UIFont.systemFont(ofSize: 15), selectedFont: UIFont.aic_mediumFont(withSize: 17))
        pageController.titleSizeNormal = 15
        pageController.titleSizeSelected = 17
        pageController.progressColor = color
        pageController.menuViewBgColor = .clear
        pageController.menuViewStyle = .default
        //        pageController.titleColorNormal = color
        //        pageController.titleColorSelected = color
        pageController.isShowTitleChangeAnimate = false
//        pageController.setCustom(normalFont: nil, selectedFont: nil)
        return pageController
    }()
    
    lazy var navBar: UIView = {
        var view = UIView()
        view.backgroundColor = UIColor.baseTheme.current.navBarColor
        return view
    }()
    
    var backButton:UIButton = UIButton()
    
    var menuFrame: CGRect {
        return CGRect(x: 0, y: aic_statusBarHeight() - 3, width: self.view.width, height: 44)
    }
    
    var rightBarButton: UIButton? {
        didSet {
            oldValue?.removeFromSuperview()
            if let button = rightBarButton {
                self.view.addSubview(button)
                button.snp.makeConstraints({ (make) in
                    make.trailing.equalTo(self.navBar).offset(-11)
                    make.centerY.equalTo(self.navBar).offset(aic_statusBarHeight() / 2)
                })
                button.aic_hitTestSlop = UIEdgeInsets(top: -10, left: -10, bottom: -10, right: -10)
            }
        }
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        self.aic_navigationBarHidden = true
        self.addUI()
        
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        let frame = self.view.bounds
        self.navBar.frame = CGRect(x: 0, y: 0, width: frame.width, height: aic_statusBarHeight() + 44)
        self.pageController.view.frame = frame
    }
    
    func addUI() {
        self.view.addSubview(self.navBar)
        self.addChild(self.pageController)
        self.view.addSubview(self.pageController.view)
        self.navBar.snp.makeConstraints { (make) in
            make.top.leading.trailing.equalTo(0)
            make.height.equalTo(aic_statusBarHeight() + 44)
        }
        
        let button = backButton
        
        let color = UIColor.baseTheme.current.navItemColor
        button.setImage(UIImage.base.image(name: "Icon_Nav_Back")?.byTintColor(color), for: .normal)
        self.view.addSubview(button)
        button.snp.makeConstraints({ (make) in
            make.leading.equalTo(self.navBar).offset(11)
            make.centerY.equalTo(self.navBar).offset(aic_statusBarHeight() / 2)
        })
        button.aic_hitTestSlop = UIEdgeInsets(top: -10, left: -10, bottom: -10, right: -10)
        button.addTarget(self, action: #selector(touchLeftItem), for: .touchUpInside)
    }
    
    //MARK: WMPageControllerDelegate, WMPageControllerDataSource
    
    func pageController(_ pageController: WMPageController, preferredFrameForContentView contentView: WMScrollView) -> CGRect {
        let menuFrame = self.menuFrame
        return CGRect(x: 0, y: menuFrame.maxY + 3, width: self.view.width, height: self.view.height - menuFrame.maxY)
    }
    
    func pageController(_ pageController: WMPageController, preferredFrameFor menuView: WMMenuView) -> CGRect {
        return self.menuFrame
    }
    
    func numbersOfChildControllers(in pageController: WMPageController) -> Int {
        return 0
    }
    
    func pageController(_ pageController: WMPageController, viewControllerAt index: Int) -> UIViewController {
        return UIViewController()
    }
    
    func pageController(_ pageController: WMPageController, titleAt index: Int) -> String {
        return ""
    }
    
    func pageController(_ pageController: WMPageController, didEnter viewController: UIViewController, withInfo info: [AnyHashable : Any]) {
        
    }
}
