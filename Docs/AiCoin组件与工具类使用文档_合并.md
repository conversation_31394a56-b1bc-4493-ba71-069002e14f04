# 1. 概述

## 1.1 项目架构简介

AICoin-iOS是一款专注于数字货币行情、资讯和交易的移动应用，采用模块化架构设计，主要由以下几个核心模块组成：

- **基础模块（Base）**：提供基础UI组件、网络请求、数据存储、工具类等底层支持
- **首页模块（HomePage）**：展示市场热门币种和概览，提供快速入口和推荐内容
- **行情模块（Ticker）**：提供各种数字货币的实时行情、K线图表和市场数据
- **自选模块**：用户自定义关注的数字货币列表和分组管理
- **资讯内容模块（Content）**：提供新闻、快讯、动态等内容服务
- **个人中心模块（Me）**：用户账户管理、设置和个性化功能
- **K线图表模块（CandleChart）**：专业的K线图表展示和分析工具
- **登录模块（Login）**：用户认证和账户管理
- **交易模块（Trade）**：数字货币交易功能

项目采用混合开发模式，主要使用Swift和Objective-C两种语言，并通过桥接文件（AICoin-Swift.h）实现两种语言的互操作。项目整体遵循MVC架构模式，同时在部分模块中引入MVVM设计模式提高代码可维护性。

### 1.1.1 目录结构

项目主要目录结构如下：

```
AICoin/
├── Module/                 # 业务模块
│   ├── Base/               # 基础组件
│   ├── HomePage/           # 首页模块
│   ├── Ticker/             # 行情模块
│   ├── 自选/               # 自选模块
│   ├── Content/            # 资讯内容模块
│   ├── Me/                 # 个人中心模块
│   ├── CandleChart/        # K线图表模块
│   ├── Login/              # 登录模块
│   └── trade/              # 交易模块
├── Resources/              # 资源文件
├── Supporting Files/       # 配置文件
└── Vendor/                 # 第三方依赖
```

### 1.1.2 技术栈

项目使用的主要技术栈包括：

- **UI框架**：UIKit
- **网络请求**：AFNetworking/AICHttpManager
- **WebSocket**：SocketRocket
- **数据库**：WCDBSwift
- **布局**：SnapKit、Masonry、Bees
- **图片加载**：YYWebImageManager
- **序列化**：SwiftyJSON、YYModel、DataDecodable
- **数据绑定**：ObservableValue
- **下拉刷新**：MJRefresh/AICRefresh
- **空视图**：DZNEmptyDataSet

## 1.2 文档目的和使用方法

本文档旨在帮助开发人员快速了解和使用AICoin-iOS项目中的基础组件和工具类，提高开发效率，保持代码风格一致性，减少重复开发工作。通过本文档，开发人员可以：

- 了解项目中可用的基础组件和工具类
- 学习各组件的使用方法和最佳实践
- 避免重复造轮子，提高开发效率
- 保持代码风格一致，提高代码质量

### 1.2.1 使用方法

1. **快速查找**：文档按功能模块分类，开发者可根据需求快速定位到相应章节
2. **代码示例**：每个组件和工具类都提供了实际使用示例，可直接复制使用
3. **最佳实践**：遵循文档中推荐的最佳实践，确保代码质量和性能
4. **版本兼容**：注意查看组件的版本兼容性说明，确保在不同iOS版本中正常工作

### 1.2.2 适用人群

- **新加入项目的iOS开发人员**：快速了解项目架构和可用组件
- **需要使用特定组件的现有团队成员**：查找特定组件的使用方法
- **进行代码审查的技术负责人**：确保代码符合项目规范和最佳实践

## 1.3 文档结构

本文档按照功能模块划分为以下几个主要部分：

### 1.3.1 主题管理

第2章详细介绍了AICoin-iOS的主题管理系统，包括：
- 白天/黑夜两种主题模式
- SSBThemeManager的使用方法
- 主题切换的实现机制
- 颜色和图片资源的主题适配

### 1.3.2 多语言支持

第3章介绍了多语言支持功能，包括：
- 简体中文/繁体中文/英文的支持
- SystemConfig的使用方法
- 语言切换的实现机制
- 本地化字符串的使用方式


### 1.3.3 布局与UI组件

第4章详细介绍了布局工具和常用UI组件，包括：
- SnapKit/Masonry/Bees布局工具的使用
- 空视图（DZNEmptyDataSet）的实现
- 弹窗组件的使用方法
- 开关组件的使用方法
- 下拉刷新组件的集成
- 图片加载的最佳实践

### 1.3.4 数据存储与序列化

第5章介绍了数据存储和序列化方案，包括：
- WCDB数据库的使用方法
- 三种序列化方案的对比和选择
- 缓存管理的实现

### 1.3.5 网络组件

第6章详细介绍了网络组件，包括：
- HTTP请求的封装和使用
- WebSocket通信的实现
- 网络缓存的管理

### 1.3.6 基础UI组件

第7章介绍了基础UI组件，包括：
- 基础控制器的使用
- 基础视图的实现
- 导航组件的集成

### 1.3.7 工具类

第8章详细介绍了常用工具类，包括：
- 字符串处理工具
- 日期处理工具
- 数字格式化工具
- 其他实用工具类

### 1.3.8 数据绑定

第9章介绍了数据绑定方案，重点是：
- ObservableValue数据绑定的实现和使用
- 属性观察者模式的应用
- 数据绑定的最佳实践

### 1.3.9 最佳实践

第10章总结了项目开发的最佳实践，包括：
- 组件选择指南
- 性能优化建议
- 常见问题解决方案
- 代码规范和风格指南


# AICoin-iOS 主题管理系统文档

## 目录

1. [主题系统概述](#1-主题系统概述)
2. [主题管理器 SSBThemeManager](#2-主题管理器-ssbthememanager)
3. [主题资源管理](#3-主题资源管理)
4. [使用方法](#4-使用方法)

## 1. 主题系统概述

AICoin-iOS应用支持白天和黑夜两种主题模式，为用户提供不同场景下的最佳视觉体验。主题系统具有以下特点：

### 1.1 主题模式

- **白天模式（DaytimeTheme）**：明亮的背景和深色文本，适合日间使用
- **黑夜模式（NighttimeTheme）**：深色背景和浅色文本，减少夜间使用时的视觉疲劳

### 1.2 主题管理方式

- **全局管理**：通过`SSBThemeManager`单例进行全局主题状态管理
- **模块化资源**：每个模块各自管理自己的主题资源（颜色和图片）
- **全局通知机制**：主题切换时通过通知机制刷新整个应用界面

### 1.3 K线独立主题

K线图表模块有独立的主题管理系统，可以不跟随应用的主题设置，这主要是考虑到专业用户可能有特定的K线图表主题偏好。

## 2. 主题管理器 SSBThemeManager

`SSBThemeManager`是AICoin应用的主题管理核心类，负责主题状态的存储、切换和通知。

### 2.1 主要功能

- 存储和获取当前主题状态
- 提供主题切换方法
- 发送主题变更通知
- 管理主题资源

### 2.2 主要方法

```swift
// 获取当前主题名称
let currentTheme = SSBThemeManager.shared().currentThemeName  // .day 或 .night

// 判断是否为夜间主题
let isNightTheme = aic_isNightTheme()

// 切换主题（带动画效果）
SSBThemeManager.shared().changeTheme(at: view) {
    // 主题切换完成后的回调
}

// 简单切换主题（无动画，无Toast提示）
SSBThemeManager.shared().simpleChangeTheme()

// 获取主题颜色（使用全局函数）
let color = colorForKey("colorKey")  // 使用全局函数获取颜色
```

## 3. 主题资源管理

AICoin应用的主题资源主要包括颜色和图片两部分，采用模块化管理方式。

### 3.1 颜色管理

颜色管理通过`BaseThemeProtocol`协议和实现类进行配置，每个模块定义自己的主题颜色协议和实现类。颜色管理的具体实现方式请参考[AICoin-iOS 基础组件和工具类使用文档](AICoin基础组件和工具类使用文档.md#2-2-2-获取主题颜色)。

#### 3.1.1 内置主题颜色

项目中已经实现了多个模块的主题颜色：

- **BaseTheme**：基础模块颜色
- **MomentTheme**：动态模块颜色
- **HomeTheme**：主页模块颜色

### 3.2 图片资源管理

图片资源通过Assets.xcassets中的特定文件夹进行管理，每个模块在自己的Assets中配置白天和黑夜两套图片资源。

#### 3.2.1 图片资源结构

```
ModuleAssets.xcassets/
├── BaseDay/              # 白天模式图片
│   ├── nav_back.imageset
│   ├── tab_home.imageset
│   └── ...
└── BaseNight/            # 黑夜模式图片
    ├── nav_back.imageset
    ├── tab_home.imageset
    └── ...
```

#### 3.2.2 图片资源协议

```swift
protocol AICImageProtocol {
    static func imageForDay(name: String) -> UIImage?
    static func imageForNight(name: String) -> UIImage?
    static func image(name: String) -> UIImage?
}

class BaseImage: AICImageProtocol {
    class func imageForDay(name: String) -> UIImage? {
        return UIImage(named: "BaseDay/\(name)")
    }

    class func imageForNight(name: String) -> UIImage? {
        return UIImage(named: "BaseNight/\(name)")
    }

    class func image(name: String) -> UIImage? {
        if aic_isNightTheme(),
           let img = self.imageForNight(name: name) {
            return img
        }
        return self.imageForDay(name: name)
    }
}

extension UIImage {
    static let base = BaseImage()
}
```

## 4. 使用方法

### 4.1 获取主题颜色

根据AICoin编码规范，获取主题颜色的方式如下：

```swift
// 基本用法
let color = UIColor.模块.current.颜色属性

// 示例：获取基础模块的副标题颜色
let subtitleColor = UIColor.baseTheme.current.cellSubtitleColor

// 示例：获取登录模块的文本颜色
let textColor = UIColor.login.current.textColor

// 使用UIColor扩展方法获取主题颜色
let themeColor = UIColor.themeColor(day: 0xFFFFFF, night: 0x121212)

// 使用UIColor扩展方法获取主题颜色（使用UIColor对象）
let themeColor = UIColor.themeColor(dayColor: .white, nightColor: .black)

// 获取K线图表专用主题颜色
let klineColor = UIColor.themeColorFollowKLine(day: 0xFFFFFF, night: 0x121212)
```

### 4.2 获取主题图片

```swift
// 基本用法
let image = UIImage.模块.image(name: "图片名称")

// 示例：获取基础模块的返回按钮图片
let backImage = UIImage.base.image(name: "nav_back")

// 示例：获取登录模块的图标
let logoImage = UIImage.login.image(name: "login_logo")
```

### 4.3 在代码中判断当前主题

```swift
// 判断是否为夜间主题
if aic_isNightTheme() {
    // 夜间主题下的特殊处理
} else {
    // 日间主题下的特殊处理
}

// 根据主题设置不同的值
let cornerRadius = aic_isNightTheme() ? 8.0 : 4.0
```

# AICoin-iOS 多语言支持系统文档

## 目录

1. [多语言系统概述](#1-多语言系统概述)
2. [SystemConfig 语言管理](#2-systemconfig-语言管理)
3. [本地化文件结构](#3-本地化文件结构)
4. [使用方法](#4-使用方法)
## 1. 多语言系统概述

AICoin-iOS应用支持简体中文、繁体中文和英文三种语言，为全球用户提供本地化的使用体验。多语言系统具有以下特点：

### 1.1 支持的语言

- **简体中文**：面向中国大陆用户
- **繁体中文**：面向港澳台及海外华人用户
- **英文**：面向国际用户

### 1.2 语言管理方式

- **集中管理**：通过`InternalToolKit`中的`SystemConfig`类统一管理语言状态
- **全局切换**：切换语言时会替换应用的`rootViewController`为新的`AICTabbarController`实例
- **持久化存储**：语言设置保存在`UserDefaults`中，应用重启后仍然保持

### 1.3 本地化资源

- **字符串资源**：各模块在对应的`.strings`文件中配置多语言文本
- **模块化管理**：每个模块管理自己的本地化资源
- **扩展方法**：通过String扩展提供便捷的本地化方法

## 2. SystemConfig 语言管理

`SystemConfig`是AICoin应用的语言管理核心类，位于`InternalToolKit`框架中，负责语言状态的存储、获取和切换。

### 2.1 核心功能

- 存储和获取当前语言设置
- 提供语言切换方法
- 判断当前界面语言类型
- 管理语言资源

### 2.2 主要API

```swift
// 获取当前语言类型
let language = SystemConfig.userInterfaceLanguage()

// 判断是否为中文界面（简体或繁体）
let isChinese = SystemConfig.isChineseUserInterface()

```

## 3. 本地化文件结构

AICoin应用的本地化资源主要通过`.strings`文件进行管理，按照语言和模块进行组织。

### 3.1 项目结构

```
AICoin/
├── Resources/
│   ├── en.lproj/                # 英文资源
│   │   ├── Localizable.strings  # 通用字符串
│   │   ├── InfoPlist.strings    # Info.plist本地化
│   │   └── ModuleLocalizable.strings  # 模块特定字符串
│   ├── zh-Hans.lproj/           # 简体中文资源
│   │   ├── Localizable.strings
│   │   ├── InfoPlist.strings
│   │   └── ModuleLocalizable.strings
│   └── zh-Hant.lproj/           # 繁体中文资源
│       ├── Localizable.strings
│       ├── InfoPlist.strings
│       └── ModuleLocalizable.strings
└── Module/
    └── ModuleName/              # 各业务模块
        └── Resources/           # 模块特定资源
            ├── en.lproj/
            ├── zh-Hans.lproj/
            └── zh-Hant.lproj/
```

### 3.2 Localizable.strings 文件

`Localizable.strings`文件是本地化字符串的主要存储位置，采用键值对的形式定义：

```
// 繁体中文 (zh-Hant.lproj/Localizable.strings)
"跟单交易" = "跟單交易";
"全仓" = "全倉";
"逐仓" = "逐倉";
"做多" = "做多";

// 英文 (en.lproj/Localizable.strings)
"跟单交易" = "Copy Trading";
"全仓" = " Cross ";
"逐仓" = " Isolated ";
"做多" = "Long";
```

### 3.3 模块特定本地化文件

对于特定模块的本地化字符串，可以创建模块专用的`.strings`文件：

```
// 行情模块 (Ticker/Resources/zh-Hant.lproj/Ticker-Localizable.strings)
"项目对" = "項目對";
"市值" = "市值";
"抱歉，暂时未找到相关搜索内容！" = "抱歉，暫時未找到相關搜索內容！";
"联系我们" = "聯繫我們";

// 行情模块 (Ticker/Resources/en.lproj/Ticker-Localizable.strings)
"项目对" = "Pairs";
"市值" = "Cap";
"抱歉，暂时未找到相关搜索内容！" = "Sorry, no relevant records!";
"联系我们" = "Contact Us";
```

## 4. 使用方法

### 4.1 获取本地化文本

根据AICoin编码规范，获取本地化文本的方式如下：

```swift
// 基本用法
let localizedString = "文本".模块.localized

// 示例：获取基础模块的本地化文本
let cancelText = "取消".base.localized

// 示例：获取行情模块的本地化文本
let marketListText = "市场列表".ticker.localized

// 在Objective-C中使用
NSString *localizedString = [@"文本" baseLocalizedForOC];
```

### 4.2 格式化本地化文本

对于包含参数的本地化文本，可以使用格式化方法：

```swift
// 定义带参数的本地化字符串
// Localizable.strings
"welcome_user" = "欢迎，%@";
"items_count" = "共有 %d 个项目";

// 使用带参数的本地化字符串
let username = "张三"
let welcomeText = String(format: "welcome_user".base.localized, username)
// 结果: "欢迎，张三"

let count = 5
let itemsText = String(format: "items_count".base.localized, count)
// 结果: "共有 5 个项目"
```

### 4.3 判断当前语言

```swift
// 判断是否为中文界面（简体或繁体）
if SystemConfig.isChineseUserInterface() {
    // 中文界面特定处理
} else {
    // 非中文界面处理
}

// 获取当前语言类型
let currentLanguage = SystemConfig.userInterfaceLanguage()
switch currentLanguage {
case .simplifiedChinese:
    print("当前是简体中文")
case .traditionalChinese:
    print("当前是繁体中文")
case .english:
    print("当前是英文")
}
```

# AICoin-iOS 布局与UI组件文档

## 目录

1. [布局工具](#1-布局工具)
2. [空视图](#2-空视图)
3. [弹窗组件](#3-弹窗组件)
4. [AICSwitch开关组件](#4-aicswitch开关组件)
5. [下拉刷新](#5-下拉刷新)
6. [图片加载](#6-图片加载)
7. [最佳实践](#7-最佳实践)

## 1. 布局工具

AICoin-iOS项目不使用Storyboard或XIB进行UI开发，而是采用代码方式实现自动布局，以适配不同设备。项目中使用了多种自动布局库，各有特点。

### 1.1 SnapKit（推荐）

SnapKit是项目中推荐使用的布局库，语法简洁，易于维护。

#### 1.1.1 基本用法

```swift
// 基本约束
view.snp.makeConstraints { make in
    make.top.equalTo(superview.snp.top).offset(10)
    make.left.right.equalToSuperview()
    make.height.equalTo(44)
}

// 更新约束
view.snp.updateConstraints { make in
    make.height.equalTo(50)
}

// 重新设置约束
view.snp.remakeConstraints { make in
    make.edges.equalToSuperview().inset(UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10))
}
```

#### 1.1.2 安全区域适配

项目中对SnapKit进行了扩展，提供了安全区域适配的便捷方法：

```swift
// 使用安全区域约束
view.snp.makeConstraints { make in
    make.top.equalTo(view.snp_safeTop)
    make.bottom.equalTo(view.snp_safeBottom)
    make.left.equalTo(view.snp_safeLeading)
    make.right.equalTo(view.snp_safeTrailing)
}
```

#### 1.1.3 Ticker模块示例

```swift
// 行情列表页面布局示例
class TickerMarketListViewController: UIViewController {

    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.register(TickerMarketListCell.self, forCellReuseIdentifier: "TickerMarketListCell")
        tableView.delegate = self
        tableView.dataSource = self
        return tableView
    }()

    private lazy var searchBar: UISearchBar = {
        let searchBar = UISearchBar()
        searchBar.placeholder = "搜索".ticker.localized
        return searchBar
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }

    private func setupUI() {
        // 添加子视图
        view.addSubview(searchBar)
        view.addSubview(tableView)
        searchBar.snp.makeConstraints { make in
            make.top.equalTo(view.snp_safeTop)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }

        tableView.snp.makeConstraints { make in
            make.top.equalTo(searchBar.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }
}
```


## 2. 空视图

AICoin项目集成了DZNEmptyDataSet来处理空数据状态的视图展示，默认AICTableView的空视图是BaseEmptyDelegateModel。

### 2.1 基本用法

```swift
// 在视图控制器中实现DZNEmptyDataSet协议
class TickerMarketListViewController: UIViewController, DZNEmptyDataSetSource, DZNEmptyDataSetDelegate {

    override func viewDidLoad() {
        super.viewDidLoad()

        // 设置空视图代理
        tableView.emptyDataSetSource = self
        tableView.emptyDataSetDelegate = self
    }

    // MARK: - DZNEmptyDataSetSource

    func title(forEmptyDataSet scrollView: UIScrollView!) -> NSAttributedString! {
        return NSAttributedString(string: "暂无数据".base.localized, attributes: [
            .font: UIFont.systemFont(ofSize: 16),
            .foregroundColor: UIColor.base.current.textColor
        ])
    }

    func description(forEmptyDataSet scrollView: UIScrollView!) -> NSAttributedString! {
        return NSAttributedString(string: "请稍后再试".base.localized, attributes: [
            .font: UIFont.systemFont(ofSize: 14),
            .foregroundColor: UIColor.base.current.subtitleColor
        ])
    }

    func image(forEmptyDataSet scrollView: UIScrollView!) -> UIImage! {
        return UIImage.base.image(name: "empty_placeholder")
    }

    // MARK: - DZNEmptyDataSetDelegate

    func emptyDataSetShouldDisplay(_ scrollView: UIScrollView!) -> Bool {
        // 控制空视图的显示条件
        return dataSource.isEmpty && !isLoading
    }

    func emptyDataSetShouldAllowScroll(_ scrollView: UIScrollView!) -> Bool {
        return true
    }

    func emptyDataSet(_ scrollView: UIScrollView!, didTap button: UIButton!) {
        // 处理空视图按钮点击
        loadData()
    }
}
```

### 2.2 使用BaseEmptyDelegateModel

AICTableView默认使用BaseEmptyDelegateModel作为空视图模型，可以简化空视图的配置：

```swift
// 创建空视图模型
let emptyModel = BaseEmptyDelegateModel()
emptyModel.title = "暂无行情数据"
emptyModel.detail = "请稍后再试"
emptyModel.image = UIImage.ticker.image(name: "empty_market")
emptyModel.buttonTitle = "刷新"
emptyModel.buttonAction = { [weak self] in
    self?.loadData()
}

// 设置到表格视图
tableView.aic_emptyDataModel = emptyModel
```

### 2.3 Ticker模块示例

```swift
// 行情列表空视图示例
func setupEmptyView() {
    let emptyModel = BaseEmptyDelegateModel()
    emptyModel.title = "暂无行情数据".ticker.localized
    emptyModel.detail = "请检查网络连接或稍后再试".ticker.localized
    emptyModel.image = UIImage.ticker.image(name: "ticker_empty")
    emptyModel.buttonTitle = "刷新".base.localized
    emptyModel.buttonAction = { [weak self] in
        self?.refreshData()
    }

    tableView.aic_emptyDataModel = emptyModel
}
```

## 3. 弹窗组件

AICoin项目提供了一套完整的自定义弹窗系统，根据使用频率统计，项目中最常用的几种弹窗组件如下：

- **AICInfoAlertController**: 最常用的信息提示弹窗
- **AICBaseCustomPresentationViewController**: 所有自定义弹窗的基类
- **AICAlertViewController**: 项目中通用的警告控制器
- **AICInfoActionSheetController**: 底部弹出的信息菜单
- **AICCustomViewAlertController**: 自定义视图警告控制器
- **AICPopoverViewController**: 气泡弹窗基类

### 3.1 AICInfoAlertController (最常用)

`AICInfoAlertController`是项目中使用最频繁的弹窗类型，用于显示标题、消息和按钮，适合简单的提示信息场景。

```swift
// 创建信息提示弹窗
func showInfoAlert() {
    // 创建带标题和消息的弹窗
    let alertVC = AICInfoAlertController(
        title: "提示信息".ticker.localized,
        message: "操作已完成".ticker.localized
    )

    // 添加按钮
    alertVC.addAction(action: AICAlertAction(
        title: "确定".ticker.localized,
        style: .default,
        handler: { _ in
            // 按钮点击回调
            print("用户点击了确定")
        }
    ))

    // 显示弹窗
    present(alertVC, animated: true, completion: nil)
}
```

### 3.2 AICBaseCustomPresentationViewController (基类)

`AICBaseCustomPresentationViewController`是所有自定义弹窗的基类，许多具体弹窗继承自此类。它提供了自定义转场动画和交互式消失功能，支持两种风格：alert(中间弹窗)和actionSheet(底部弹出)。

```swift
// 创建自定义弹窗
class CustomAlertViewController: AICBaseCustomPresentationViewController {

    init() {
        // 使用alert样式初始化（居中显示）
        super.init(style: .alert)
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }

    // 必须覆写preferredContentSize
    override var preferredContentSize: CGSize {
        get {
            return CGSize(width: 280, height: 200)
        }
        set {
            super.preferredContentSize = newValue
        }
    }

    private func setupUI() {
        // 设置弹窗内容和布局
        view.backgroundColor = UIColor.base.current.backgroundColor
        // 添加子视图和设置约束（此处省略具体实现）
    }
}

// 底部弹出样式的弹窗
class BottomSheetViewController: AICBaseCustomPresentationViewController {

    init() {
        super.init(style: .actionSheet)
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // 必须覆写preferredContentSize
    override var preferredContentSize: CGSize {
        get {
            return CGSize(width: UIScreen.main.bounds.width, height: 300)
        }
        set {
            super.preferredContentSize = newValue
        }
    }
}
```

### 3.3 AICAlertViewController (通用警告控制器)

`AICAlertViewController`是项目中通用的警告控制器，提供预设样式，使用频率较高。它简化了弹窗创建过程，支持标题、自定义内容视图和多个操作按钮。

```swift
// 创建通用警告弹窗 - 通过继承使用
class CustomAlertViewController: AICAlertViewController {

    init() {
        // 创建自定义内容视图
        let contentView = CustomAlertContentView()

        super.init(
            title: "操作确认".ticker.localized,
            customView: contentView,
            hasCancelBtn: true
        )
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupActions()
    }

    private func setupActions() {
        // 添加取消按钮
        addAction(BaseAlertAction(
            title: "取消".ticker.localized,
            type: .cancel,
            action: nil
        ))

        // 添加确认按钮
        addAction(BaseAlertAction(
            title: "确认".ticker.localized,
            type: .default,
            action: {
                // 按钮点击回调
                self.performAction()
            }
        ))
    }

    private func performAction() {
        // 执行具体操作
    }
}

// 使用示例
func showAlert() {
    let alertVC = CustomAlertViewController()
    present(alertVC, animated: true, completion: nil)
}

// 不带标题的弹窗
class NoTitleAlertViewController: AICAlertViewController {

    init() {
        let contentView = CustomContentView()
        super.init(customView: contentView, hasCancelBtn: true)
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
```

### 3.4 AICCustomViewAlertController (自定义视图)

`AICCustomViewAlertController`用于完全自定义内容的场景，适合需要复杂UI的弹窗。

```swift
// 创建自定义内容弹窗 - 通过继承使用
class CustomViewAlertViewController: AICCustomViewAlertController {

    init() {
        // 创建自定义内容视图
        let contentView = CustomView()
        contentView.frame = CGRect(x: 0, y: 0, width: 280, height: 200)

        super.init(customView: contentView)
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        // 可以在这里添加额外的设置
    }
}

// 使用示例
func showCustomAlert() {
    let alertVC = CustomViewAlertViewController()
    present(alertVC, animated: true, completion: nil)
}
```

### 3.5 AICInfoActionSheetController (底部菜单)

`AICInfoActionSheetController`用于从底部弹出的菜单选项，适合提供多个操作选项的场景。

```swift
// 创建底部操作菜单
func showActionSheet() {
    // 创建底部菜单
    let actionSheet = AICInfoActionSheetController(
        title: "请选择操作".ticker.localized,
        detail: "从以下选项中选择一项".ticker.localized
    )

    // 添加操作按钮
    actionSheet.addAction(AICAlertAction(
        title: "编辑".ticker.localized,
        style: .default,
        handler: { _ in
            self.editItem()
        }
    ))

    actionSheet.addAction(AICAlertAction(
        title: "删除".ticker.localized,
        style: .destructive,
        handler: { _ in
            self.deleteItem()
        }
    ))

    actionSheet.addAction(AICAlertAction(
        title: "取消".ticker.localized,
        style: .cancel,
        handler: nil
    ))

    // 显示底部菜单
    present(actionSheet, animated: true, completion: nil)
}
```

### 3.6 AICPopoverViewController (气泡弹窗)

`AICPopoverViewController`是气泡弹窗基类，用于小型上下文菜单，可以指定箭头位置。

```swift
// 创建气泡弹窗
func showPopover(from sourceView: UIView) {
    // 创建气泡弹窗内容
    let contentVC = PopoverContentViewController()

    // 创建气泡弹窗
    let popoverVC = AICPopoverViewController()
    popoverVC.contentViewController = contentVC

    // 设置气泡样式
    popoverVC.style.position = .bottom
    popoverVC.style.arrowSize = 8
    popoverVC.style.cornerRadius = 8

    // 显示气泡弹窗
    popoverVC.show(from: sourceView, in: self.view)
}
```

## 4. AICSwitch开关组件

AICoin项目提供了自定义的开关组件`AICSwitch`，它是基于`SevenSwitch`的封装，支持主题适配和自定义样式。

### 4.1 基本特性

- 自动适配主题颜色（日间/夜间模式）
- 支持阴影效果
- 固定尺寸设计（37x13）
- 扩大点击区域，提升用户体验
- 圆角设计，视觉效果更佳

### 4.2 基本用法

```swift
// 创建开关
let aicSwitch = AICSwitch()

// 添加事件监听
aicSwitch.addTarget(self, action: #selector(switchValueChanged(_:)), for: .valueChanged)

// 设置开关状态
aicSwitch.on = true

// 获取开关状态
let isOn = aicSwitch.isOn

// 添加到视图
view.addSubview(aicSwitch)
aicSwitch.snp.makeConstraints { make in
    make.right.equalToSuperview().offset(-16)
    make.centerY.equalToSuperview()
}

// 处理开关状态变化
@objc func switchValueChanged(_ sender: AICSwitch) {
    if sender.isOn {
        // 开关打开时的处理
        print("开关已打开")
    } else {
        // 开关关闭时的处理
        print("开关已关闭")
    }
}
```

### 4.3 在表格中使用

AICoin项目提供了`BaseSwitchTableViewCell`，方便在表格中使用开关组件：

```swift
// 创建带开关的表格单元格
let cell = tableView.dequeueReusableCell(
    withIdentifier: "SwitchCell",
    for: indexPath
) as! BaseSwitchTableViewCell

// 配置单元格
cell.titleLabel.text = "开启通知".base.localized
cell.aSwitch.on = isNotificationEnabled

// 处理开关状态变化
cell.touchSwitchCallBack = { isOn in
    // 更新设置
    self.updateNotificationSetting(isEnabled: isOn)
}

return cell
```

### 4.4 主题适配

`AICSwitch`会自动适配当前主题，但也可以手动更新颜色：

```swift
// 主题切换时手动更新颜色
NotificationCenter.default.addObserver(self,
                                      selector: #selector(themeDidChange),
                                      name: NSNotification.Name(SSBThemeManagerShouldChangeTheme),
                                      object: nil)

@objc private func themeDidChange() {
    // 更新开关颜色
    aicSwitch.updateColor()
}
```

## 5. 下拉刷新

AICoin项目对MJRefresh进行了封装，提供了`AICRefreshNormalHeader`和`AICRefreshAutoNormalFooter`，支持主题切换和国际化。

### 5.1 基本用法

```swift
// 添加下拉刷新
tableView.mj_header = AICRefreshNormalHeader(refreshingBlock: { [weak self] in
    // 刷新数据
    self?.loadData()
})

// 添加上拉加载更多
tableView.mj_footer = AICRefreshAutoNormalFooter(refreshingBlock: { [weak self] in
    // 加载更多数据
    self?.loadMoreData()
})

// 结束刷新
tableView.mj_header?.endRefreshing()
tableView.mj_footer?.endRefreshing()

// 没有更多数据
tableView.mj_footer?.endRefreshingWithNoMoreData()

// 重置没有更多数据状态
tableView.mj_footer?.resetNoMoreData()
```

### 5.2 自定义刷新样式

```swift
// 自定义下拉刷新样式
let header = AICRefreshNormalHeader(refreshingBlock: { [weak self] in
    self?.loadData()
})
header.setTitle("下拉刷新".base.localized, for: .idle)
header.setTitle("释放更新".base.localized, for: .pulling)
header.setTitle("正在刷新...".base.localized, for: .refreshing)
header.lastUpdatedTimeLabel?.isHidden = true  // 隐藏最后更新时间
tableView.mj_header = header

// 自定义上拉加载样式
let footer = AICRefreshAutoNormalFooter(refreshingBlock: { [weak self] in
    self?.loadMoreData()
})
footer.setTitle("点击或上拉加载更多".base.localized, for: .idle)
footer.setTitle("正在加载...".base.localized, for: .refreshing)
footer.setTitle("没有更多数据".base.localized, for: .noMoreData)
tableView.mj_footer = footer
```

### 5.3 Ticker模块示例

```swift
// 行情列表刷新示例
func setupRefresh() {
    // 下拉刷新
    tableView.mj_header = AICRefreshNormalHeader(refreshingBlock: { [weak self] in
        // 刷新行情数据
        self?.viewModel.refreshMarketList { [weak self] success in
            self?.tableView.mj_header?.endRefreshing()
            if success {
                self?.tableView.reloadData()
            }
        }
    })

    // 上拉加载更多
    tableView.mj_footer = AICRefreshAutoNormalFooter(refreshingBlock: { [weak self] in
        // 加载更多行情数据
        self?.viewModel.loadMoreMarkets { [weak self] (success, hasMore) in
            if !hasMore {
                self?.tableView.mj_footer?.endRefreshingWithNoMoreData()
            } else {
                self?.tableView.mj_footer?.endRefreshing()
            }

            if success {
                self?.tableView.reloadData()
            }
        }
    })
}
```

## 6. 图片加载

AICoin项目使用`YYWebImageManager`进行异步图片加载和缓存，通过扩展方法简化调用。

### 6.1 基本用法

```swift
// 基本用法
imageView.aic_setImage(with: URL(string: urlString))

// 带占位图
imageView.aic_setImage(with: URL(string: urlString), placeholder: UIImage(named: "placeholder"))

// 带回调
imageView.aic_setImage(with: URL(string: urlString), placeholder: nil, options: [], completion: { (image, url, _, _, error) in
    // 处理加载完成后的逻辑
})
```

### 6.2 高级选项

```swift
// 设置图片加载选项
let options: YYWebImageOptions = [
    .setImageWithFadeAnimation,  // 淡入动画
    .progressiveBlur,            // 渐进式模糊
    .allowBackgroundTask         // 允许后台任务
]

// 加载并处理图片
imageView.aic_setImage(with: URL(string: urlString), placeholder: placeholderImage, options: options) { (image, url, _, _, error) in
    if let error = error {
        print("图片加载失败: \(error.localizedDescription)")
    } else if let image = image {
        // 图片加载成功，可以进行额外处理
        self.processLoadedImage(image)
    }
}
```

### 6.3 Ticker模块示例

```swift
// 行情列表中加载币种图标示例
class TickerMarketListCell: UITableViewCell {

    private let iconImageView = UIImageView()
    private let nameLabel = UILabel()
    private let priceLabel = UILabel()

    func configure(with model: TickerMarketListModel) {
        // 设置币种名称、价格等其他数据
        nameLabel.text = model.coinShow
        priceLabel.text = model.lastPriceText

        // 加载币种图标
        if let iconURL = URL(string: model.logo) {
            // 使用YYWebImageManager加载图片
            iconImageView.aic_setImage(with: iconURL,
                                      placeholder: UIImage.ticker.image(name: "default_coin_icon"))
        } else {
            // 使用默认图标
            iconImageView.image = UIImage.ticker.image(name: "default_coin_icon")
        }
    }
}
```

## 7. 最佳实践

### 7.1 布局最佳实践

- 优先使用SnapKit进行布局，避免使用frame和CGRect
- 使用安全区域适配方法适配不同设备
- 避免硬编码尺寸，使用相对尺寸和比例
- 使用UIStackView简化线性布局
- 复杂UI拆分为小组件，提高复用性

### 7.2 空视图最佳实践

- 为不同的空状态设计不同的提示信息和图标
- 提供明确的操作指引，如刷新按钮
- 空视图文案应简洁明了，避免技术术语
- 考虑首次加载、网络错误、无数据等不同场景

### 7.3 弹窗最佳实践

- 使用AICBaseAlertController而非UIAlertController
- 弹窗内容保持简洁，避免过多信息
- 提供明确的操作按钮，避免用户困惑
- 考虑键盘弹出时的布局调整

### 7.4 下拉刷新最佳实践

- 避免自动触发刷新，除非必要
- 提供明确的视觉反馈，如刷新状态文本
- 考虑首次加载时是否显示刷新控件
- 合理使用无更多数据状态

### 7.5 图片加载最佳实践

- 始终提供占位图，避免布局跳动
- 使用适当的图片缓存策略
- 考虑列表滚动时的图片加载性能
- 处理图片加载失败的情况

### 7.6 开关组件最佳实践

- 使用AICSwitch而非系统UISwitch，保持视觉一致性
- 在主题切换时调用updateColor()方法更新颜色
- 使用BaseSwitchTableViewCell简化表格中的开关使用
- 为开关提供明确的标签说明其功能
- 考虑开关状态变化时的用户反馈


# AICoin-iOS 数据存储与序列化文档

## 目录

1. [WCDB数据库](#1-wcdb数据库)
2. [序列化方案](#2-序列化方案)
3. [缓存管理](#3-缓存管理)
4. [最佳实践](#4-最佳实践)

## 1. WCDB数据库

AICoin-iOS项目使用WCDB.Swift进行数据持久化，它是微信团队开发的高性能、ACID兼容的移动数据库框架，支持一句代码即可将数据取出并组合为对象，支持语言集成查询和多线程高并发。

### 1.1 数据库模型定义

在WCDB中，数据库模型需要实现`TableCodable`协议，并定义表结构映射。

#### 1.1.1 基本模型定义

```swift
import WCDBSwift

// 行情币种数据库模型
final class TickerCurrencyDBModel: AICTickerBaseModel, TableCodable {
    
    // 模型属性
    var key: String = ""         // 币种唯一标识
    var enName: String = ""      // 英文名称
    var cnName: String = ""      // 中文名称
    var logo: String = ""        // 图标URL
    var rank: Int = 0            // 排名
    var coinShow: String = ""    // 显示名称
    
    // 表结构映射
    enum CodingKeys: String, CodingTableKey {
        case key
        case enName = "en_name"
        case cnName = "cn_name"
        case logo
        case rank
        case coinShow = "coin_show"
        
        // 定义Root类型
        typealias Root = TickerCurrencyDBModel
        
        // 对象关系映射
        static let objectRelationalMapping = TableBinding(CodingKeys.self)
        
        // 定义主键和索引
        static var columnConstraintBindings: [CodingKeys: ColumnConstraintBinding]? {
            return [
                key: ColumnConstraintBinding(isPrimary: true)
            ]
        }
    }
}
```

#### 1.1.2 复杂数据类型处理

对于复杂数据类型（如字典、数组），WCDB不能直接存储，需要转换为字符串或其他基本类型：

```swift
// 行情消息数据库模型
final class TickerMessageDBModel: TableCodable {
    var messageID: Int = 0
    var content: String = ""
    var extraDataJSON: String = "{}"  // 存储JSON字符串
    var timestamp: Int = 0
    
    enum CodingKeys: String, CodingTableKey {
        typealias Root = TickerMessageDBModel
        static let objectRelationalMapping = TableBinding(CodingKeys.self)
        
        case messageID
        case content
        case extraDataJSON = "extra_data"
        case timestamp
    }
    
    // 字典数据的计算属性
    var extraData: [String: Any] {
        get {
            if let jsonData = extraDataJSON.data(using: .utf8),
               let dict = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any] {
                return dict
            }
            return [:]
        }
        set {
            if let jsonData = try? JSONSerialization.data(withJSONObject: newValue),
               let jsonString = String(data: jsonData, encoding: .utf8) {
                extraDataJSON = jsonString
            }
        }
    }
}
```

### 1.2 数据库操作

#### 1.2.1 数据库初始化

```swift
// 行情数据库管理器
class TickerDBManager {
    // 单例
    static let shared = TickerDBManager()
    private init() {
        setupDatabase()
    }
    
    // 数据库和表名
    private let database: Database
    private let currencyTableName = "ticker_currency"
    private let marketTableName = "ticker_market"
    
    // 初始化数据库
    private func setupDatabase() {
        let dbPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first! + "/ticker.db"
        database = Database(withPath: dbPath)
        
        do {
            // 创建币种表
            try database.create(table: currencyTableName, of: TickerCurrencyDBModel.self)
            
            // 创建市场表
            try database.create(table: marketTableName, of: TickerMarketDBModel.self)
            
            // 创建索引
            try database.create(index: ["key"], on: currencyTableName, named: "currency_key_index")
            try database.create(index: ["market_list_key"], on: marketTableName, named: "market_key_index")
        } catch {
            aic_log("创建表失败: \(error)")
        }
    }
}
```

#### 1.2.2 CRUD操作

```swift
// 插入或更新币种数据
func insertOrUpdateCurrency(_ model: TickerCurrencyDBModel) -> Bool {
    do {
        try database.insertOrReplace(objects: model, intoTable: currencyTableName)
        return true
    } catch {
        aic_log("插入币种失败: \(error)")
        return false
    }
}

// 查询币种数据
func getCurrency(key: String) -> TickerCurrencyDBModel? {
    do {
        return try database.getObject(fromTable: currencyTableName, 
                                     where: TickerCurrencyDBModel.Properties.key == key)
    } catch {
        aic_log("查询币种失败: \(error)")
        return nil
    }
}

// 查询所有币种
func getAllCurrencies() -> [TickerCurrencyDBModel] {
    do {
        return try database.getObjects(fromTable: currencyTableName)
    } catch {
        aic_log("查询所有币种失败: \(error)")
        return []
    }
}

// 删除币种
func deleteCurrency(key: String) -> Bool {
    do {
        try database.delete(fromTable: currencyTableName, 
                           where: TickerCurrencyDBModel.Properties.key == key)
        return true
    } catch {
        aic_log("删除币种失败: \(error)")
        return false
    }
}
```

#### 1.2.3 高级查询

```swift
// 分页查询市场数据
func getMarkets(page: Int, pageSize: Int) -> [TickerMarketDBModel] {
    do {
        let offset = (page - 1) * pageSize
        return try database.getObjects(
            fromTable: marketTableName,
            orderBy: [TickerMarketDBModel.Properties.rank.asOrder(by: .ascending)],
            limit: pageSize,
            offset: offset
        )
    } catch {
        aic_log("分页查询市场失败: \(error)")
        return []
    }
}

// 条件查询
func searchMarkets(keyword: String) -> [TickerMarketDBModel] {
    do {
        // 使用LIKE进行模糊查询
        return try database.getObjects(
            fromTable: marketTableName,
            where: TickerMarketDBModel.Properties.coinShow.like("%\(keyword)%") 
                || TickerMarketDBModel.Properties.platformShow.like("%\(keyword)%")
        )
    } catch {
        aic_log("搜索市场失败: \(error)")
        return []
    }
}

// 复杂条件查询
func getTopMarkets(platform: String, limit: Int) -> [TickerMarketDBModel] {
    do {
        return try database.getObjects(
            fromTable: marketTableName,
            where: TickerMarketDBModel.Properties.platform == platform,
            orderBy: [TickerMarketDBModel.Properties.volume24h.asOrder(by: .descending)],
            limit: limit
        )
    } catch {
        aic_log("查询热门市场失败: \(error)")
        return []
    }
}
```

#### 1.2.4 事务处理

```swift
// 批量插入市场数据
func batchInsertMarkets(_ markets: [TickerMarketDBModel]) -> Bool {
    do {
        // 使用事务处理批量操作
        try database.run(transaction: {
            for market in markets {
                try database.insertOrReplace(objects: market, intoTable: marketTableName)
            }
        })
        return true
    } catch {
        aic_log("批量插入市场失败: \(error)")
        return false
    }
}

// 清空并重建表
func rebuildMarketTable(with markets: [TickerMarketDBModel]) -> Bool {
    do {
        try database.run(transaction: {
            // 删除所有数据
            try database.delete(fromTable: marketTableName)
            
            // 插入新数据
            for market in markets {
                try database.insert(objects: market, intoTable: marketTableName)
            }
        })
        return true
    } catch {
        aic_log("重建市场表失败: \(error)")
        return false
    }
}
```

### 1.3 Ticker模块示例

以下是行情模块中使用WCDB的实际示例：

```swift
// 行情模块视图控制器
class TickerMarketListViewController: AICBaseViewController {
    
    private let viewModel = TickerMarketListViewModel()
    private lazy var tableView = UITableView()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
    }
    
    private func loadData() {
        // 先从数据库加载缓存数据
        viewModel.loadCachedMarkets { [weak self] success in
            if success {
                self?.tableView.reloadData()
            }
            
            // 然后请求网络数据
            self?.viewModel.refreshMarketList { [weak self] success in
                self?.tableView.mj_header?.endRefreshing()
                if success {
                    self?.tableView.reloadData()
                }
            }
        }
    }
}

// 行情列表视图模型
class TickerMarketListViewModel {
    
    private(set) var markets: [TickerMarketListModel] = []
    
    // 加载缓存数据
    func loadCachedMarkets(completion: @escaping (Bool) -> Void) {
        AICSwiftTool.runonGlobalQueueAsync {
            // 从数据库加载数据
            let dbMarkets = TickerDBManager.shared.getAllMarkets()
            
            // 转换为视图模型
            let marketModels = dbMarkets.map { dbModel -> TickerMarketListModel in
                let model = TickerMarketListModel()
                model.marketListKey = dbModel.marketListKey
                model.coinShow = dbModel.coinShow
                model.platformShow = dbModel.platformShow
                model.logo = dbModel.logo
                // 设置其他属性...
                return model
            }
            
            AICSwiftTool.runonMainQueue {
                self.markets = marketModels
                completion(!marketModels.isEmpty)
            }
        }
    }
    
    // 刷新市场列表
    func refreshMarketList(completion: @escaping (Bool) -> Void) {
        AICTickerRequestOperation.shared.requestMarketList { [weak self] (models, error) in
            guard let self = self, let models = models else {
                completion(false)
                return
            }
            
            self.markets = models
            
            // 将数据保存到数据库
            AICSwiftTool.runonGlobalQueueAsync {
                // 转换为数据库模型
                let dbModels = models.map { model -> TickerMarketDBModel in
                    let dbModel = TickerMarketDBModel()
                    dbModel.marketListKey = model.marketListKey
                    dbModel.coinShow = model.coinShow
                    dbModel.platformShow = model.platformShow
                    dbModel.logo = model.logo
                    // 设置其他属性...
                    return dbModel
                }
                
                // 批量保存到数据库
                _ = TickerDBManager.shared.batchInsertMarkets(dbModels)
            }
            
            completion(true)
        }
    }
}
```

## 2. 序列化方案

AICoin-iOS项目中使用了三种不同的序列化方案，各有优缺点。

### 2.1 SwiftyJSON

SwiftyJSON是一个轻量级的JSON解析库，需要指定映射关系及数据类型，实现比较啰嗦。

#### 2.1.1 基本用法

```swift
// 解析JSON
func parseTickerData(_ data: Any) -> TickerModel? {
    let json = JSON(data)
    
    // 检查数据有效性
    guard json["success"].boolValue else {
        let errorCode = json["errorCode"].intValue
        let errorMessage = json["error"].stringValue
        aic_log("API错误: \(errorCode) - \(errorMessage)")
        return nil
    }
    
    // 解析数据
    let tickerData = json["data"]
    
    let ticker = TickerModel()
    ticker.tickerId = tickerData["id"].stringValue
    ticker.name = tickerData["name"].stringValue
    ticker.symbol = tickerData["symbol"].stringValue
    ticker.price = tickerData["price"].doubleValue
    ticker.change24h = tickerData["change_24h"].doubleValue
    ticker.volume24h = tickerData["volume_24h"].doubleValue
    
    // 解析嵌套数据
    if let marketData = tickerData["market"].dictionary {
        ticker.marketName = marketData["name"]?.stringValue ?? ""
        ticker.marketLogo = marketData["logo"]?.stringValue ?? ""
    }
    
    // 解析数组
    ticker.priceHistory = tickerData["price_history"].arrayValue.map { $0.doubleValue }
    
    return ticker
}
```

#### 2.1.2 Ticker模块示例

```swift
// 行情请求操作
class AICTickerRequestOperation {
    
    static let shared = AICTickerRequestOperation()
    
    func requestMarketList(completion: @escaping ([TickerMarketListModel]?, Error?) -> Void) {
        AICHttpManager.shared.post("/api/v1/ticker/market/list", parameters: nil, progress: nil, success: { task, response in
            let json = JSON(response ?? "")
            
            if json["success"].boolValue {
                let dataArray = json["data"].arrayValue
                var models: [TickerMarketListModel] = []
                
                for itemJson in dataArray {
                    let model = TickerMarketListModel()
                    model.marketListKey = itemJson["market_list_key"].stringValue
                    model.coinShow = itemJson["coin_show"].stringValue
                    model.platformShow = itemJson["platform_show"].stringValue
                    model.logo = itemJson["logo"].stringValue
                    model.lastPrice = itemJson["last_price"].doubleValue
                    model.degree24h = itemJson["degree_24h"].doubleValue
                    
                    models.append(model)
                }
                
                completion(models, nil)
            } else {
                let errorCode = json["errorCode"].intValue
                let errorMessage = json["error"].stringValue
                let error = NSError(domain: "APIError", code: errorCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                completion(nil, error)
            }
        }, failure: { task, error in
            completion(nil, error)
        })
    }
}
```

### 2.2 YYModel

YYModel利用了OC的特性进行自动映射，Swift类使用需声明@objcMembers。

#### 2.2.1 基本用法

```swift
// 定义模型
@objcMembers
class TickerDetailModel: NSObject {
    var tickerId: String = ""
    var name: String = ""
    var symbol: String = ""
    var price: NSNumber = 0
    var change24h: NSNumber = 0
    var volume24h: NSNumber = 0
    var marketCap: NSNumber = 0
    var marketInfo: MarketInfo?
    var priceHistory: [NSNumber] = []
    
    // 自定义映射关系
    class func modelCustomPropertyMapper() -> [String: String] {
        return [
            "tickerId": "id",
            "change24h": "change_24h",
            "volume24h": "volume_24h",
            "marketCap": "market_cap",
            "marketInfo": "market",
            "priceHistory": "price_history"
        ]
    }
}

@objcMembers
class MarketInfo: NSObject {
    var name: String = ""
    var logo: String = ""
}

// 使用YYModel解析
func parseTickerDetail(_ data: [String: Any]) -> TickerDetailModel? {
    return TickerDetailModel.model(withJSON: data)
}
```

#### 2.2.2 Ticker模块示例

```swift
// 行情详情请求
func requestTickerDetail(tickerId: String, completion: @escaping (TickerDetailModel?, Error?) -> Void) {
    let params = ["ticker_id": tickerId]
    
    AICHttpManager.shared.post("/api/v1/ticker/detail", parameters: params, progress: nil, success: { task, response in
        if let responseDict = response as? [String: Any],
           let success = responseDict["success"] as? Bool, success,
           let data = responseDict["data"] as? [String: Any] {
            
            // 使用YYModel解析
            let model = TickerDetailModel.model(withJSON: data)
            completion(model, nil)
        } else {
            let error = NSError(domain: "APIError", code: -1, userInfo: [NSLocalizedDescriptionKey: "数据解析失败"])
            completion(nil, error)
        }
    }, failure: { task, error in
        completion(nil, error)
    })
}
```

### 2.3 DataDecodable

DataDecodable继承Swift 4.2 Decodable协议，完善类型转换，Swift类方便调用，但不支持继承的类。

#### 2.3.1 基本用法

```swift
// 定义协议
protocol DataDecodable: Decodable {
    init(from data: Any) throws
}

// 默认实现
extension DataDecodable {
    init(from data: Any) throws {
        let jsonData: Data
        
        if let dictionary = data as? [String: Any] {
            jsonData = try JSONSerialization.data(withJSONObject: dictionary)
        } else if let array = data as? [Any] {
            jsonData = try JSONSerialization.data(withJSONObject: array)
        } else if let string = data as? String, let stringData = string.data(using: .utf8) {
            jsonData = stringData
        } else {
            throw DecodingError.dataCorrupted(DecodingError.Context(
                codingPath: [],
                debugDescription: "Invalid data type"
            ))
        }
        
        self = try JSONDecoder().decode(Self.self, from: jsonData)
    }
}

// 定义模型
struct TickerAlertModel: DataDecodable {
    let alertId: String
    let tickerId: String
    let price: Double
    let direction: String
    let createdAt: Int
    
    enum CodingKeys: String, CodingKey {
        case alertId = "alert_id"
        case tickerId = "ticker_id"
        case price
        case direction
        case createdAt = "created_at"
    }
}

// 使用DataDecodable解析
func parseAlertData(_ data: Any) -> TickerAlertModel? {
    do {
        return try TickerAlertModel(from: data)
    } catch {
        aic_log("解析预警数据失败: \(error)")
        return nil
    }
}
```

#### 2.3.2 Ticker模块示例

```swift
// 行情预警请求
func requestTickerAlerts(completion: @escaping ([TickerAlertModel]?, Error?) -> Void) {
    AICBaseHttpManager.post("user/alerts", parameters: ["userid": UserManager.share.safeUserID]) { response in
        if response.success, let data = response.data {
            do {
                // 解析数组数据
                if let alertsData = data["alerts"] as? [[String: Any]] {
                    let alerts = try alertsData.map { try TickerAlertModel(from: $0) }
                    completion(alerts, nil)
                } else {
                    throw NSError(domain: "ParseError", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的预警数据"])
                }
            } catch {
                completion(nil, error)
            }
        } else {
            completion(nil, response.error)
        }
    }
}
```

## 3. 缓存管理

AICoin-iOS项目使用`AICCacheManager`进行缓存管理，支持内存缓存和磁盘缓存。

### 3.1 缓存管理器

`AICCacheManager`是一个单例类，提供了缓存的存取、清理等功能。

```swift
// 获取缓存管理器单例
let cacheManager = AICCacheManager.share

// 缓存对象
cacheManager.setObject(userInfo, forKey: "currentUser")

// 获取缓存对象
let cachedUser = cacheManager.object(forKey: "currentUser") as? UserInfo

// 缓存图片
cacheManager.setImage(image, forKey: "userAvatar")

// 获取缓存图片
let cachedImage = cacheManager.image(forKey: "userAvatar")

// 清除特定缓存
cacheManager.removeObject(forKey: "currentUser")

// 清除所有缓存
cacheManager.removeAllObjects()
```

### 3.2 网络缓存

项目使用`AICNetworkCache`进行网络响应数据的缓存：

```swift
// 缓存网络响应数据
AICNetworkCache.shared.cache(key: cacheKey, value: model, expireSeconds: 60 * 5)  // 缓存5分钟

// 获取缓存数据
if let cachedModel: TickerMarketListModel = AICNetworkCache.shared.object(forKey: cacheKey) {
    // 使用缓存数据
    self.updateUI(with: cachedModel)
}

// 清除缓存
AICNetworkCache.shared.removeObject(forKey: cacheKey)
AICNetworkCache.shared.removeAllObjects()  // 清除所有缓存
```

### 3.3 Ticker模块示例

```swift
// 行情列表缓存示例
class TickerMarketListViewModel {
    
    private let cacheKey = "ticker_market_list"
    private(set) var markets: [TickerMarketListModel] = []
    
    // 加载数据（优先使用缓存）
    func loadData(completion: @escaping (Bool) -> Void) {
        // 尝试从缓存加载
        if let cachedMarkets: [TickerMarketListModel] = AICNetworkCache.shared.object(forKey: cacheKey) {
            self.markets = cachedMarkets
            completion(true)
            
            // 后台刷新最新数据
            refreshDataInBackground()
            return
        }
        
        // 缓存不存在，请求网络数据
        refreshData(completion: completion)
    }
    
    // 刷新数据
    func refreshData(completion: @escaping (Bool) -> Void) {
        AICTickerRequestOperation.shared.requestMarketList { [weak self] (models, error) in
            guard let self = self, let models = models else {
                completion(false)
                return
            }
            
            self.markets = models
            
            // 缓存数据，有效期10分钟
            AICNetworkCache.shared.cache(key: self.cacheKey, value: models, expireSeconds: 10 * 60)
            
            completion(true)
        }
    }
    
    // 后台刷新数据
    private func refreshDataInBackground() {
        AICSwiftTool.runonGlobalQueueAsync {
            self.refreshData { _ in }
        }
    }
}
```

## 4. 最佳实践

### 4.1 WCDB最佳实践

1. **模型设计**
   - 使用适当的数据类型，避免滥用字符串类型
   - 为频繁查询的字段创建索引
   - 使用主键确保数据唯一性

2. **性能优化**
   - 使用事务处理批量操作
   - 避免在主线程执行数据库操作
   - 合理设计表结构，避免过度复杂的查询

3. **错误处理**
   - 始终使用try-catch处理数据库操作
   - 记录数据库错误，便于调试
   - 提供数据恢复机制

### 4.2 序列化最佳实践

1. **选择合适的序列化方案**
   - SwiftyJSON：适用于灵活的JSON解析，但代码较啰嗦
   - YYModel：适用于与OC混编的项目，自动映射省时省力
   - DataDecodable：适用于纯Swift项目，类型安全且简洁

2. **错误处理**
   - 验证JSON数据的有效性
   - 提供默认值处理缺失字段
   - 记录解析错误，便于调试

3. **性能考虑**
   - 避免重复解析相同数据
   - 大型数据集考虑异步解析
   - 缓存解析结果减少重复工作

### 4.3 缓存最佳实践

1. **缓存策略**
   - 设置合理的缓存过期时间
   - 根据数据重要性选择内存缓存或磁盘缓存
   - 定期清理过期缓存

2. **缓存键设计**
   - 使用有意义的缓存键名
   - 避免键名冲突
   - 考虑添加版本信息到键名

3. **缓存监控**
   - 监控缓存大小，避免过度占用内存
   - 在内存警告时清理非必要缓存
   - 提供手动清理缓存的选项

# AICoin iOS 网络层对接文档

## 一、网络层架构概述

AICoin iOS应用采用多层网络架构，主要由以下几个核心组件组成：

1. **AICHttpManager** - 核心底层网络请求管理器，基于AFNetworking封装，位于Pods中
2. **AICNetworkCache** - 网络缓存管理器，用于缓存API响应数据

这些组件共同构成了一个功能丰富、高效可靠的网络层，支持不同业务模块的网络需求。

## 二、核心网络组件详解

### 1. AICHttpManager

`AICHttpManager`是应用网络请求的核心底层组件，基于AFNetworking封装，提供了统一的网络请求接口。该组件的具体实现被封装在Pods中，项目大部分接口请求都直接使用`AICHttpManager.shared.post`等方法来发起请求。

#### 1.1 主要功能

- 网络请求的发送与响应处理
- 加密与签名验证
- 网络状态监控
- 请求参数预处理
- 全局错误处理

#### 1.2 请求方法

```objc
// 基本POST请求
[AICHttpManager.shared POST:@"/api/v1/endpoint"
    parameters:params
    progress:nil
    success:^(NSURLSessionDataTask *task, id responseObject) {
        // 处理成功响应
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        // 处理错误
    }];
```

Swift调用示例：
```swift
// 基本POST请求
AICHttpManager.shared.post("/api/v1/endpoint", parameters: params, progress: nil, success: { (task, response) in
    // 处理成功响应
}, failure: { (task, error) in
    // 处理错误
})
```
#### 1.3 网络状态监测

AICHttpManager提供了网络状态监测功能：

```swift
// 检查网络是否可用
if AICHttpManager.shared.isReachable {
    // 网络可用，执行网络请求
} else {
    // 网络不可用，提示用户
}

// 监听网络状态变化
NotificationCenter.default.addObserver(self,
                                       selector: #selector(networkingChanged),
                                       name: .AICNetworkingChangedInNotice,
                                       object: nil)
```

#### 1.4 异步/等待(async/await)支持

Swift 5.5+支持使用async/await语法：

```swift
// 使用async/await (Swift 5.5+)
Task {
    let (task, response) = await AICHttpManager.asyncPost("/api/v1/endpoint", parameters: params)
    let json = JSON(response ?? "")
    if json["success"].boolValue {
        // 处理成功响应
    }
}
```

### 2. AICNetworkCache

`AICNetworkCache`是一个专门用于缓存网络响应数据的工具类，可以减少重复请求，提高应用性能。

#### 2.1 主要功能

- 内存缓存 - 使用NSCache实现高效的内存缓存
- 磁盘缓存 - 将数据缓存到本地文件系统
- 自动过期 - 支持设置缓存过期时间
- 内存警告处理 - 在系统内存不足时自动清理缓存

#### 2.2 使用方法

```swift
// 获取缓存实例
let cache = AICNetworkCache.shared

// 缓存数据到内存
cache.cache(key: "api/v1/ticker", value: responseData, expireSeconds: 60)

// 从内存获取缓存
if let cachedData: Data = cache.get(for: "api/v1/ticker") {
    // 使用缓存数据
}

// 缓存数据到磁盘
cache.cacheToDisk(key: "api/v1/ticker", value: responseModel, expireSeconds: 300)

// 从磁盘获取缓存
if let cachedModel: ResponseModel = cache.getFromDisk(for: "api/v1/ticker") {
    // 使用缓存数据
}

// 清除特定缓存
cache.removeCache(for: "api/v1/ticker")

// 清除所有缓存
cache.removeAllCache()

// 清除磁盘缓存
cache.clearDiskCache()

// 清除过期的磁盘缓存
cache.clearExpiredDiskCache()
```

## 三、网络请求流程

### 1. 标准请求流程

1. 创建请求参数
2. 调用对应的网络请求方法
3. 等待响应回调
4. 解析响应数据
5. 处理业务逻辑

```swift
// 示例：获取币种行情
func requestTickerData(tickerId: String, completion: @escaping (TickerModel?, Error?) -> Void) {
    let params = ["id": tickerId]

    AICHttpManager.shared.post("/api/v1/ticker", parameters: params, progress: nil, success: { [weak self] task, response in
        guard let self = self else { return }
        let json = JSON(response ?? "")
        if json["success"].boolValue {
            let data = json["data"]
            let model = TickerModel.model(withJSON: data)
            completion(model, nil)
        } else {
            completion(nil, NSError(domain: "APIError", code: json["errorCode"].intValue, userInfo: [NSLocalizedDescriptionKey: json["error"].stringValue]))
        }
    }, failure: { task, error in
        completion(nil, error)
    })
}
```

### 2. 缓存请求流程

1. 检查内存/磁盘缓存
2. 如果缓存有效，使用缓存数据
3. 如果缓存无效或不存在，发起网络请求
4. 接收响应后更新缓存

```swift
// 示例：带缓存的行情请求
func requestTickerDataWithCache(tickerId: String, completion: @escaping (TickerModel?, Error?) -> Void) {
    let cacheKey = "ticker_\(tickerId)"

    // 检查缓存
    if let cachedModel: TickerModel = AICNetworkCache.shared.get(for: cacheKey) {
        completion(cachedModel, nil)
        return
    }

    // 缓存不存在，发起网络请求
    requestTickerData(tickerId: tickerId) { (model, error) in
        if let model = model {
            // 更新缓存
            AICNetworkCache.shared.cache(key: cacheKey, value: model, expireSeconds: 60)
        }
        completion(model, error)
    }
}
```


## 四、最佳实践

### 1. 网络层封装

建议为每个业务模块创建专门的网络请求管理类，封装该模块的所有API调用：

```swift
// 示例：行情模块网络管理器
class TickerNetworkManager {
    static let shared = TickerNetworkManager()

    // 获取行情列表
    func requestTickerList(completion: @escaping ([TickerModel]?, Error?) -> Void) {
        AICHttpManager.shared.post("/api/v1/ticker/list", parameters: nil, progress: nil, success: { task, response in
            let json = JSON(response ?? "")
            if json["success"].boolValue {
                let dataArray = json["data"].arrayValue
                let models = dataArray.compactMap { TickerModel.model(withJSON: $0) }
                completion(models, nil)
            } else {
                completion(nil, NSError(domain: "APIError", code: json["errorCode"].intValue, userInfo: nil))
            }
        }, failure: { task, error in
            completion(nil, error)
        })
    }

    // 获取K线数据
    func requestKLineData(tickerId: String, period: String, completion: @escaping ([KLineModel]?, Error?) -> Void) {
        // 实现请求逻辑
    }
}
```

### 2. 取消请求

在视图控制器即将消失时，应该取消尚未完成的网络请求：

```swift
class TickerViewController: AICBaseViewController {
    var dataTask: URLSessionDataTask?

    func requestData() {
        // 取消之前的请求
        dataTask?.cancel()

        // 发起新的请求
        dataTask = AICHttpManager.shared.post("/api/v1/ticker", parameters: nil, progress: nil, success: { [weak self] task, response in
            guard let self = self else { return }
            // 处理响应
        }, failure: { [weak self] task, error in
            guard let self = self else { return }
            // 处理错误
        })
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        // 取消未完成的请求
        dataTask?.cancel()
    }
}
```

### 3. 错误重试

对于重要请求，可以实现自动重试机制：

```swift
func requestWithRetry(retryCount: Int = 3, completion: @escaping (Bool, Any?) -> Void) {
    func attempt(remainingAttempts: Int) {
        AICHttpManager.shared.post("/api/v1/important_data", parameters: nil, progress: nil, success: { task, response in
            let json = JSON(response ?? "")
            completion(json["success"].boolValue, json["data"])
        }, failure: { task, error in
            if remainingAttempts > 0 {
                // 请求失败但还有重试次数，延迟后重试
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    attempt(remainingAttempts: remainingAttempts - 1)
                }
            } else {
                // 无重试次数，返回失败结果
                completion(false, error)
            }
        })
    }

    // 开始第一次尝试
    attempt(remainingAttempts: retryCount)
}
```

### 4. 请求防抖

对于用户输入触发的请求，应实现防抖机制避免频繁请求：

```swift
var searchTask: DispatchWorkItem?

func searchCoins(keyword: String) {
    // 取消之前的延迟搜索任务
    searchTask?.cancel()

    // 创建新的延迟搜索任务
    let task = DispatchWorkItem { [weak self] in
        guard let self = self else { return }

        // 发起搜索请求
        AICHttpManager.shared.post("/api/v1/search", parameters: ["keyword": keyword], progress: nil, success: { task, response in
            // 处理搜索结果
            let json = JSON(response ?? "")
            if json["success"].boolValue {
                // 处理搜索结果
            }
        }, failure: { task, error in
            // 处理错误
        })
    }

    // 延迟执行
    searchTask = task
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute: task)
}
```

## 五、WebSocket通信

除了RESTful API外，AICoin还使用WebSocket进行实时数据通信，主要通过`WebSocketHelper`类和`TickerSubscribeDataManager`类实现。

### 1. WebSocket基础连接

```swift
// 获取WebSocket助手实例
let wsHelper = WebSocketHelper(host: "wss://api.aicoin.com/ws")

// 添加观察者
wsHelper.add(observer: self)

// 建立连接
wsHelper.connectWebSocket()

// 发送消息
wsHelper.sendData("{\"type\":\"ping\", \"userid\":\"12345\"}")

// 断开连接
wsHelper.disconnectWebSocket()
```

### 2. 实现观察者协议

```swift
extension MyViewController: WebSocketHelperProtocol {
    // 连接成功
    func webSocketHelperDidOpen(_ helper: WebSocketHelper) {
        print("WebSocket连接已建立")
        // 可以开始订阅数据
    }

    // 收到消息
    func webSocketHelperDidReceiveData(_ helper: WebSocketHelper, text: String) {
        // 处理收到的消息
        if let data = text.data(using: .utf8),
           let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            print("收到WebSocket消息: \(json)")
        }
    }

    // 连接关闭
    func webSocketHelperDidClose(_ helper: WebSocketHelper, error: Error?) {
        print("WebSocket连接已关闭: \(error?.localizedDescription ?? "未知错误")")
    }
}
```

### 3. 使用TickerSubscribeDataManager订阅行情数据

AICoin使用`TickerSubscribeDataManager`管理WebSocket订阅，它提供了多种订阅方法，是获取实时行情数据的推荐方式。

#### 3.1 订阅单个交易对价格

```swift
// 示例：订阅BTC/USDT在Binance交易所的最新价格
func subscribeBTCUSDT() {
    // 获取交易对模型
    guard let marketModel = AICTickerDataBase.share.fetchMarketListModel(withKey: "binance_btcusdt") else {
        return
    }

    // 订阅单个交易对
    let observation = TickerSubscribeDataManager.shared.sub(market: marketModel)

    // 保持对observation的引用，当不再需要时会自动取消订阅
    self.observation = observation

    // 监听数据更新
    NotificationCenter.default.addObserver(self,
                                          selector: #selector(handleTickerUpdate(_:)),
                                          name: .tickerDataDidUpdate,
                                          object: nil)
}

// 处理价格更新通知
@objc func handleTickerUpdate(_ notification: Notification) {
    if let userInfo = notification.userInfo,
       let marketListKey = userInfo["marketListKey"] as? String,
       marketListKey == "binance_btcusdt" {
        // 从数据池获取最新价格
        if let model = TickerTempDataPool.shared.marketModel(forKey: "binance_btcusdt") {
            let price = model.price
            let change = model.change
            print("BTC/USDT最新价格: \(price), 24h涨跌幅: \(change)%")

            // 更新UI
            DispatchQueue.main.async {
                self.priceLabel.text = price
                self.changeLabel.text = "\(change)%"
                self.changeLabel.textColor = change.doubleValue >= 0 ? .baseCurrentUpColor : .baseCurrentDownColor
            }
        }
    }
}
```

#### 3.2 订阅多个交易对价格

```swift
// 示例：订阅多个交易对的价格
func subscribeMultipleMarkets() {
    // 获取要订阅的交易对列表
    let marketKeys = ["binance_btcusdt", "binance_ethusdt", "okex_btcusdt"]
    let markets = marketKeys.compactMap { AICTickerDataBase.share.fetchMarketListModel(withKey: $0) }

    // 批量订阅
    let observation = TickerSubscribeDataManager.shared.sub(markets: markets)

    // 保持对observation的引用
    self.observation = observation

    // 监听数据更新
    NotificationCenter.default.addObserver(self,
                                          selector: #selector(handleTickerUpdate(_:)),
                                          name: .tickerDataDidUpdate,
                                          object: nil)
}

// 处理多个交易对的价格更新
@objc func handleTickerUpdate(_ notification: Notification) {
    if let userInfo = notification.userInfo,
       let marketListKey = userInfo["marketListKey"] as? String {
        // 检查是否是我们关注的交易对
        if ["binance_btcusdt", "binance_ethusdt", "okex_btcusdt"].contains(marketListKey) {
            // 从数据池获取最新价格
            if let model = TickerTempDataPool.shared.marketModel(forKey: marketListKey) {
                print("\(marketListKey) 最新价格: \(model.price), 24h涨跌幅: \(model.change)%")

                // 更新UI
                DispatchQueue.main.async {
                    self.updateUI(for: marketListKey, with: model)
                }
            }
        }
    }
}
```

#### 3.3 订阅深度数据

```swift
// 示例：订阅BTC/USDT的深度数据
func subscribeDepth() {
    // 订阅深度数据
    let observation = TickerSubscribeDataManager.shared.sub(depth: "binance_btcusdt") { [weak self] (depthModel) in
        // 处理深度数据
        let asks = depthModel.asks // 卖单
        let bids = depthModel.bids // 买单

        print("BTC/USDT深度数据更新:")
        print("卖单数量: \(asks.count)")
        print("买单数量: \(bids.count)")

        // 更新UI
        DispatchQueue.main.async {
            self?.updateDepthUI(asks: asks, bids: bids)
        }
    }

    // 保持对observation的引用
    self.depthObservation = observation
}
```

#### 3.4 订阅成交数据

```swift
// 示例：订阅BTC/USDT的成交数据
func subscribeTrades() {
    // 订阅成交数据
    let observation = TickerSubscribeDataManager.shared.sub(trades: "binance_btcusdt") { [weak self] (tradesModel) in
        // 处理成交数据
        let trades = tradesModel.trades

        print("BTC/USDT最新成交:")
        for trade in trades {
            print("价格: \(trade.price), 数量: \(trade.amount), 方向: \(trade.type == 1 ? "买入" : "卖出")")
        }

        // 更新UI
        DispatchQueue.main.async {
            self?.updateTradesUI(trades: trades)
        }
    }

    // 保持对observation的引用
    self.tradesObservation = observation
}
```

### 4. WebSocket消息格式

#### 4.1 订阅消息格式

订阅单个交易对的WebSocket消息格式：

```json
{
  "type": "sub_single",
  "params": ["binance_btcusdt"]
}
```

订阅多个交易对的WebSocket消息格式：

```json
{
  "type": "sub_single",
  "params": ["binance_btcusdt", "binance_ethusdt", "okex_btcusdt"]
}
```

订阅深度数据的WebSocket消息格式：

```json
{
  "type": "sub_depth",
  "params": ["binance_btcusdt"],
  "level": 10
}
```

订阅成交数据的WebSocket消息格式：

```json
{
  "type": "sub_trades",
  "params": ["binance_btcusdt"]
}
```

#### 4.2 响应数据格式

价格更新的WebSocket响应格式：

```json
{
  "type": "ticker",
  "data": {
    "marketListKey": "binance_btcusdt",
    "price": "45678.12",
    "change": "2.35",
    "high": "46123.45",
    "low": "44789.01",
    "volume": "1234.56",
    "amount": "56789012.34",
    "time": 1634567890123
  }
}
```

深度数据的WebSocket响应格式：

```json
{
  "type": "depth",
  "data": {
    "marketListKey": "binance_btcusdt",
    "asks": [
      ["45680.12", "1.2345"],
      ["45685.67", "0.5678"]
    ],
    "bids": [
      ["45675.43", "2.3456"],
      ["45670.89", "1.7890"]
    ],
    "time": 1634567890123
  }
}
```

成交数据的WebSocket响应格式：

```json
{
  "type": "trades",
  "data": {
    "marketListKey": "binance_btcusdt",
    "trades": [
      {
        "price": "45678.12",
        "amount": "0.1234",
        "type": 1,
        "time": 1634567890123
      },
      {
        "price": "45679.34",
        "amount": "0.0567",
        "type": 2,
        "time": 1634567890456
      }
    ]
  }
}
```

### 5. WebSocket错误处理与重连

```swift
// 处理WebSocket连接错误
func webSocketHelperDidClose(_ helper: WebSocketHelper, error: Error?) {
    print("WebSocket连接已关闭: \(error?.localizedDescription ?? "未知错误")")

    // 可以在这里实现重连逻辑
    DispatchQueue.main.asyncAfter(deadline: .now() + 5) { [weak self] in
        self?.reconnectWebSocket()
    }
}

// 重连WebSocket
func reconnectWebSocket() {
    // TickerSubscribeDataManager会自动处理重连和重新订阅
    // 如果直接使用WebSocketHelper，需要手动重连
    wsHelper.connectWebSocket()
}
```

### 6. 完整示例：实时监控BTC价格

```swift
class BTCPriceViewController: UIViewController {
    @IBOutlet weak var priceLabel: UILabel!
    @IBOutlet weak var changeLabel: UILabel!

    // 保持对订阅对象的引用
    private var observation: TickerSubscribeObservation?

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        subscribeBTCPrice()
    }

    private func setupUI() {
        priceLabel.text = "加载中..."
        changeLabel.text = "--"
    }

    private func subscribeBTCPrice() {
        // 获取BTC/USDT交易对模型
        guard let marketModel = AICTickerDataBase.share.fetchMarketListModel(withKey: "binance_btcusdt") else {
            priceLabel.text = "获取交易对失败"
            return
        }

        // 订阅价格更新
        observation = TickerSubscribeDataManager.shared.sub(market: marketModel)

        // 监听价格更新通知
        NotificationCenter.default.addObserver(self, selector: #selector(handlePriceUpdate(_:)), name: .tickerDataDidUpdate, object: nil)
    }

    @objc private func handlePriceUpdate(_ notification: Notification) {
        if let userInfo = notification.userInfo,
           let marketListKey = userInfo["marketListKey"] as? String,
           marketListKey == "binance_btcusdt" {
            // 从数据池获取最新价格
            if let model = TickerTempDataPool.shared.marketModel(forKey: "binance_btcusdt") {
                // 更新UI
                DispatchQueue.main.async { [weak self] in
                    guard let self = self else { return }
                    self.priceLabel.text = model.price
                    self.changeLabel.text = "\(model.change)%"
                    self.changeLabel.textColor = model.change.doubleValue >= 0 ? .baseCurrentUpColor : .baseCurrentDownColor
                }
            }
        }
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        // 移除通知观察者
        NotificationCenter.default.removeObserver(self, name: .tickerDataDidUpdate, object: nil)

        // 不再需要时，observation会自动取消订阅
        // 也可以手动设置为nil
        observation = nil
    }
}
```

# AICoin-iOS 基础UI组件文档

## 目录

1. [基础控制器](#1-基础控制器)
2. [基础视图](#2-基础视图)
3. [导航组件](#3-导航组件)
4. [最佳实践](#4-最佳实践)

## 1. 基础控制器

AICoin-iOS项目提供了一系列基础控制器类，所有视图控制器必须继承自这些基础类，以确保统一的行为和外观。

### 1.1 AICBaseViewController

`AICBaseViewController`是所有视图控制器的基类，提供了通用的生命周期管理、导航栏配置和主题适配等功能。

#### 1.1.1 基本用法

```swift
// 创建自定义视图控制器
class TickerListViewController: AICBaseViewController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // 自定义设置
        setupUI()
        loadData()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "行情".ticker.localized
        view.backgroundColor = UIColor.base.current.backgroundColor
        
        // 添加子视图
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - Data
    private func loadData() {
        // 加载数据
    }
}
```

#### 1.1.2 生命周期方法

`AICBaseViewController`重写了以下生命周期方法，提供了统一的行为：

```swift
override func viewDidLoad() {
    super.viewDidLoad()
    // 基础设置，如背景色、导航栏等
}

override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    // 设置导航栏样式
    // 注册通知监听
}

override func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)
    // 页面统计
}

override func viewWillDisappear(_ animated: Bool) {
    super.viewWillDisappear(animated)
    // 取消网络请求
    // 移除通知监听
}
```

#### 1.1.3 导航栏配置

`AICBaseViewController`提供了导航栏配置方法：

```swift
// 设置导航栏标题
self.title = "行情详情"

// 设置导航栏左侧按钮
self.navigationItem.leftBarButtonItem = UIBarButtonItem(image: UIImage.base.image(name: "nav_back"), 
                                                       style: .plain, 
                                                       target: self, 
                                                       action: #selector(backAction))

// 设置导航栏右侧按钮
self.navigationItem.rightBarButtonItem = UIBarButtonItem(title: "刷新".base.localized, 
                                                        style: .plain, 
                                                        target: self, 
                                                        action: #selector(refreshAction))

// 隐藏导航栏
self.navigationController?.setNavigationBarHidden(true, animated: true)

// 自定义返回按钮事件
@objc private func backAction() {
    self.navigationController?.popViewController(animated: true)
}
```

#### 1.1.4 主题适配

`AICBaseViewController`支持主题切换：

```swift
// 注册主题变化通知
NotificationCenter.default.addObserver(self, 
                                      selector: #selector(themeDidChange), 
                                      name: NSNotification.Name(SSBThemeManagerShouldChangeTheme), 
                                      object: nil)

// 处理主题变化
@objc private func themeDidChange() {
    // 更新视图颜色
    view.backgroundColor = UIColor.base.current.backgroundColor
    tableView.backgroundColor = UIColor.base.current.backgroundColor
    // 更新其他UI元素
    updateTheme()
}

// 自定义主题更新方法
private func updateTheme() {
    titleLabel.textColor = UIColor.base.current.textColor
    descriptionLabel.textColor = UIColor.base.current.subtitleColor
    separatorView.backgroundColor = UIColor.base.current.separatorColor
}
```

### 1.2 AICBaseTableViewController

`AICBaseTableViewController`是表格视图控制器的基类，继承自`AICBaseViewController`，提供了表格视图的基本配置和管理。

#### 1.2.1 基本用法

```swift
// 创建自定义表格视图控制器
class TickerListTableViewController: AICBaseTableViewController {
    
    private var markets: [TickerMarketListModel] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 配置表格
        tableView.register(TickerMarketCell.self, forCellReuseIdentifier: "TickerMarketCell")
        tableView.rowHeight = 70
        
        // 添加下拉刷新
        tableView.mj_header = AICRefreshNormalHeader(refreshingBlock: { [weak self] in
            self?.refreshData()
        })
        
        // 加载数据
        loadData()
    }
    
    // MARK: - UITableViewDataSource
    override func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return markets.count
    }
    
    override func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "TickerMarketCell", for: indexPath) as! TickerMarketCell
        let model = markets[indexPath.row]
        cell.configure(with: model)
        return cell
    }
    
    // MARK: - UITableViewDelegate
    override func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let model = markets[indexPath.row]
        let detailVC = TickerDetailViewController(marketKey: model.marketListKey)
        navigationController?.pushViewController(detailVC, animated: true)
    }
    
    // MARK: - Data
    private func loadData() {
        // 加载数据
        TickerNetworkManager.shared.requestMarketList { [weak self] (models, error) in
            guard let self = self else { return }
            
            self.tableView.mj_header?.endRefreshing()
            
            if let models = models {
                self.markets = models
                self.tableView.reloadData()
            } else {
                // 处理错误
                self.showToast(message: "加载失败，请重试")
            }
        }
    }
    
    private func refreshData() {
        loadData()
    }
}
```

#### 1.2.2 空视图配置

`AICBaseTableViewController`集成了`DZNEmptyDataSet`，提供了默认的空视图配置：

```swift
// 自定义空视图
let emptyModel = BaseEmptyDelegateModel()
emptyModel.title = "暂无数据".ticker.localized
emptyModel.detail = "请稍后再试".ticker.localized
emptyModel.image = UIImage.ticker.image(name: "empty_market")
emptyModel.buttonTitle = "刷新".base.localized
emptyModel.buttonAction = { [weak self] in
    self?.refreshData()
}

// 设置到表格视图
tableView.aic_emptyDataModel = emptyModel
```

### 1.3 AICBaseNavigationController

`AICBaseNavigationController`是导航控制器的基类，提供了统一的导航栏样式和行为。

#### 1.3.1 基本用法

```swift
// 创建导航控制器
let homeVC = HomeViewController()
let navController = AICBaseNavigationController(rootViewController: homeVC)

// 设置为根视图控制器
window.rootViewController = navController
```

#### 1.3.2 导航栏样式

`AICBaseNavigationController`提供了统一的导航栏样式设置：

```swift
// 设置导航栏背景色
navigationBar.barTintColor = UIColor.base.current.navigationBarColor

// 设置导航栏标题样式
navigationBar.titleTextAttributes = [
    NSAttributedString.Key.foregroundColor: UIColor.base.current.navigationTitleColor,
    NSAttributedString.Key.font: UIFont.systemFont(ofSize: 18, weight: .medium)
]

// 设置导航栏按钮样式
navigationBar.tintColor = UIColor.base.current.navigationTintColor

// 设置导航栏阴影
navigationBar.shadowImage = UIImage() // 移除阴影
```

### 1.4 AICBasePageViewController

`AICBasePageViewController`是分页视图控制器的基类，继承自`UIPageViewController`，提供了分页管理和滑动切换功能。

#### 1.4.1 基本用法

```swift
// 创建分页视图控制器
class TickerTabPageViewController: AICBasePageViewController {
    
    private let titles = ["行情", "自选", "热门"]
    private var viewControllers: [UIViewController] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 初始化子视图控制器
        let marketVC = TickerMarketViewController()
        let favoriteVC = TickerFavoriteViewController()
        let hotVC = TickerHotViewController()
        
        viewControllers = [marketVC, favoriteVC, hotVC]
        
        // 设置初始页面
        if let firstVC = viewControllers.first {
            setViewControllers([firstVC], direction: .forward, animated: false, completion: nil)
        }
        
        // 设置代理
        dataSource = self
        delegate = self
        
        // 设置标题栏
        setupTitleBar()
    }
    
    private func setupTitleBar() {
        // 实现标题栏
    }
    
    // 切换到指定页面
    func switchToPage(at index: Int) {
        guard index >= 0, index < viewControllers.count else { return }
        
        let direction: UIPageViewController.NavigationDirection = index > currentIndex ? .forward : .reverse
        setViewControllers([viewControllers[index]], direction: direction, animated: true, completion: nil)
        currentIndex = index
    }
}

// 实现UIPageViewControllerDataSource
extension TickerTabPageViewController: UIPageViewControllerDataSource {
    func pageViewController(_ pageViewController: UIPageViewController, viewControllerBefore viewController: UIViewController) -> UIViewController? {
        guard let index = viewControllers.firstIndex(of: viewController), index > 0 else {
            return nil
        }
        return viewControllers[index - 1]
    }
    
    func pageViewController(_ pageViewController: UIPageViewController, viewControllerAfter viewController: UIViewController) -> UIViewController? {
        guard let index = viewControllers.firstIndex(of: viewController), index < viewControllers.count - 1 else {
            return nil
        }
        return viewControllers[index + 1]
    }
}

// 实现UIPageViewControllerDelegate
extension TickerTabPageViewController: UIPageViewControllerDelegate {
    func pageViewController(_ pageViewController: UIPageViewController, didFinishAnimating finished: Bool, previousViewControllers: [UIViewController], transitionCompleted completed: Bool) {
        if completed, let visibleVC = pageViewController.viewControllers?.first, let index = viewControllers.firstIndex(of: visibleVC) {
            currentIndex = index
            // 更新标题栏选中状态
            updateTitleBar(selectedIndex: index)
        }
    }
}
```

## 2. 基础视图

AICoin-iOS项目提供了一系列基础视图类，用于构建统一风格的UI界面。

### 2.1 AICBaseTableView

`AICBaseTableView`是表格视图的基类，提供了统一的样式和行为。

#### 2.1.1 基本用法

```swift
// 创建表格视图
let tableView = AICBaseTableView(frame: .zero, style: .plain)
tableView.backgroundColor = UIColor.base.current.backgroundColor
tableView.separatorStyle = .none
tableView.rowHeight = 60
tableView.delegate = self
tableView.dataSource = self

// 注册单元格
tableView.register(TickerMarketCell.self, forCellReuseIdentifier: "TickerMarketCell")

// 添加到视图
view.addSubview(tableView)
tableView.snp.makeConstraints { make in
    make.edges.equalToSuperview()
}
```

#### 2.1.2 空视图配置

`AICBaseTableView`集成了`DZNEmptyDataSet`，可以轻松配置空视图：

```swift
// 设置空视图模型
let emptyModel = BaseEmptyDelegateModel()
emptyModel.title = "暂无数据".ticker.localized
emptyModel.detail = "请稍后再试".ticker.localized
emptyModel.image = UIImage.ticker.image(name: "empty_market")
emptyModel.buttonTitle = "刷新".base.localized
emptyModel.buttonAction = { [weak self] in
    self?.refreshData()
}

// 设置到表格视图
tableView.aic_emptyDataModel = emptyModel
```

### 2.2 AICBaseCollectionView

`AICBaseCollectionView`是集合视图的基类，提供了统一的样式和行为。

#### 2.2.1 基本用法

```swift
// 创建布局
let layout = UICollectionViewFlowLayout()
layout.itemSize = CGSize(width: (UIScreen.main.bounds.width - 40) / 3, height: 120)
layout.minimumLineSpacing = 10
layout.minimumInteritemSpacing = 10
layout.sectionInset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)

// 创建集合视图
let collectionView = AICBaseCollectionView(frame: .zero, collectionViewLayout: layout)
collectionView.backgroundColor = UIColor.base.current.backgroundColor
collectionView.delegate = self
collectionView.dataSource = self

// 注册单元格
collectionView.register(TickerCoinCell.self, forCellWithReuseIdentifier: "TickerCoinCell")

// 添加到视图
view.addSubview(collectionView)
collectionView.snp.makeConstraints { make in
    make.edges.equalToSuperview()
}
```

### 2.3 AICBaseLabel

`AICBaseLabel`是标签的基类，提供了统一的样式和主题适配。

#### 2.3.1 基本用法

```swift
// 创建标签
let titleLabel = AICBaseLabel()
titleLabel.text = "比特币".ticker.localized
titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
titleLabel.textColor = UIColor.base.current.textColor
titleLabel.textAlignment = .left

// 添加到视图
view.addSubview(titleLabel)
titleLabel.snp.makeConstraints { make in
    make.top.equalToSuperview().offset(10)
    make.left.equalToSuperview().offset(15)
    make.right.equalToSuperview().offset(-15)
    make.height.equalTo(20)
}
```

### 2.4 AICBaseButton

`AICBaseButton`是按钮的基类，提供了统一的样式和主题适配。

#### 2.4.1 基本用法

```swift
// 创建按钮
let refreshButton = AICBaseButton(type: .custom)
refreshButton.setTitle("刷新".base.localized, for: .normal)
refreshButton.setTitleColor(UIColor.base.current.buttonTitleColor, for: .normal)
refreshButton.backgroundColor = UIColor.base.current.buttonBackgroundColor
refreshButton.layer.cornerRadius = 5
refreshButton.clipsToBounds = true
refreshButton.addTarget(self, action: #selector(refreshAction), for: .touchUpInside)

// 添加到视图
view.addSubview(refreshButton)
refreshButton.snp.makeConstraints { make in
    make.bottom.equalToSuperview().offset(-20)
    make.centerX.equalToSuperview()
    make.width.equalTo(200)
    make.height.equalTo(44)
}
```

### 2.5 AICBaseTextView

`AICBaseTextView`是文本视图的基类，提供了统一的样式和主题适配。

#### 2.5.1 基本用法

```swift
// 创建文本视图
let textView = AICBaseTextView()
textView.font = UIFont.systemFont(ofSize: 14)
textView.textColor = UIColor.base.current.textColor
textView.backgroundColor = UIColor.base.current.textViewBackgroundColor
textView.layer.cornerRadius = 5
textView.clipsToBounds = true
textView.delegate = self

// 添加到视图
view.addSubview(textView)
textView.snp.makeConstraints { make in
    make.top.equalTo(titleLabel.snp.bottom).offset(10)
    make.left.equalToSuperview().offset(15)
    make.right.equalToSuperview().offset(-15)
    make.height.equalTo(100)
}
```

## 3. 导航组件

AICoin-iOS项目提供了一系列导航相关的组件，用于构建统一的导航体验。

### 3.1 AICBaseNavigationBar

`AICBaseNavigationBar`是自定义导航栏的基类，提供了统一的样式和行为，可以替代系统导航栏使用。

#### 3.1.1 基本用法

```swift
// 创建自定义导航栏
let navigationBar = AICBaseNavigationBar()
navigationBar.title = "行情详情".ticker.localized
navigationBar.leftButtonImage = UIImage.base.image(name: "nav_back")
navigationBar.rightButtonTitle = "更多".base.localized

// 设置点击事件
navigationBar.leftButtonAction = { [weak self] in
    self?.navigationController?.popViewController(animated: true)
}

navigationBar.rightButtonAction = { [weak self] in
    self?.showMoreOptions()
}

// 添加到视图
view.addSubview(navigationBar)
navigationBar.snp.makeConstraints { make in
    make.top.equalTo(view.snp_safeTop)
    make.left.right.equalToSuperview()
    make.height.equalTo(44)
}

// 内容视图需要下移
contentView.snp.makeConstraints { make in
    make.top.equalTo(navigationBar.snp.bottom)
    make.left.right.bottom.equalToSuperview()
}
```

#### 3.1.2 自定义样式

`AICBaseNavigationBar`支持自定义样式：

```swift
// 设置标题样式
navigationBar.titleFont = UIFont.systemFont(ofSize: 18, weight: .medium)
navigationBar.titleColor = UIColor.base.current.navigationTitleColor

// 设置背景色
navigationBar.backgroundColor = UIColor.base.current.navigationBarColor

// 设置按钮样式
navigationBar.leftButtonTintColor = UIColor.base.current.navigationTintColor
navigationBar.rightButtonTintColor = UIColor.base.current.navigationTintColor

// 设置分割线
navigationBar.showBottomLine = true
navigationBar.bottomLineColor = UIColor.base.current.separatorColor
```

#### 3.1.3 自定义视图

`AICBaseNavigationBar`支持添加自定义视图：

```swift
// 创建自定义标题视图
let titleView = UIView(frame: CGRect(x: 0, y: 0, width: 200, height: 44))

let titleLabel = UILabel()
titleLabel.text = "BTC/USDT"
titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .bold)
titleLabel.textColor = UIColor.base.current.textColor
titleLabel.textAlignment = .center

let subtitleLabel = UILabel()
subtitleLabel.text = "Binance"
subtitleLabel.font = UIFont.systemFont(ofSize: 12)
subtitleLabel.textColor = UIColor.base.current.subtitleColor
subtitleLabel.textAlignment = .center

titleView.addSubview(titleLabel)
titleView.addSubview(subtitleLabel)

titleLabel.snp.makeConstraints { make in
    make.top.equalToSuperview().offset(4)
    make.centerX.equalToSuperview()
    make.height.equalTo(20)
}

subtitleLabel.snp.makeConstraints { make in
    make.top.equalTo(titleLabel.snp.bottom)
    make.centerX.equalToSuperview()
    make.height.equalTo(16)
}

// 设置自定义标题视图
navigationBar.customTitleView = titleView
```

### 3.2 AICTabBar

`AICTabBar`是自定义标签栏的基类，提供了统一的样式和行为。

#### 3.2.1 基本用法

```swift
// 创建自定义标签栏
let tabBar = AICTabBar()

// 配置标签项
let homeItem = AICTabBarItem(title: "首页".base.localized, 
                            image: UIImage.base.image(name: "tab_home_normal"), 
                            selectedImage: UIImage.base.image(name: "tab_home_selected"))

let marketItem = AICTabBarItem(title: "行情".ticker.localized, 
                              image: UIImage.ticker.image(name: "tab_market_normal"), 
                              selectedImage: UIImage.ticker.image(name: "tab_market_selected"))

let meItem = AICTabBarItem(title: "我的".base.localized, 
                          image: UIImage.base.image(name: "tab_me_normal"), 
                          selectedImage: UIImage.base.image(name: "tab_me_selected"))

tabBar.items = [homeItem, marketItem, meItem]
tabBar.selectedIndex = 0

// 设置点击事件
tabBar.itemTapAction = { [weak self] index in
    self?.switchToViewController(at: index)
}

// 添加到视图
view.addSubview(tabBar)
tabBar.snp.makeConstraints { make in
    make.left.right.equalToSuperview()
    make.bottom.equalTo(view.snp_safeBottom)
    make.height.equalTo(49)
}
```

#### 3.2.2 自定义样式

`AICTabBar`支持自定义样式：

```swift
// 设置背景色
tabBar.backgroundColor = UIColor.base.current.tabBarBackgroundColor

// 设置文字颜色
tabBar.normalTitleColor = UIColor.base.current.tabBarNormalColor
tabBar.selectedTitleColor = UIColor.base.current.tabBarSelectedColor

// 设置文字字体
tabBar.titleFont = UIFont.systemFont(ofSize: 10)

// 设置分割线
tabBar.showTopLine = true
tabBar.topLineColor = UIColor.base.current.separatorColor
```

### 3.3 AICTabBarController

`AICTabBarController`是自定义标签栏控制器，继承自`UITabBarController`，提供了统一的样式和行为。

#### 3.3.1 基本用法

```swift
// 创建标签栏控制器
let tabBarController = AICTabBarController()

// 创建子视图控制器
let homeVC = HomeViewController()
homeVC.tabBarItem = UITabBarItem(title: "首页".base.localized, 
                                image: UIImage.base.image(name: "tab_home_normal"), 
                                selectedImage: UIImage.base.image(name: "tab_home_selected"))

let marketVC = TickerViewController()
marketVC.tabBarItem = UITabBarItem(title: "行情".ticker.localized, 
                                  image: UIImage.ticker.image(name: "tab_market_normal"), 
                                  selectedImage: UIImage.ticker.image(name: "tab_market_selected"))

let meVC = MeViewController()
meVC.tabBarItem = UITabBarItem(title: "我的".base.localized, 
                              image: UIImage.base.image(name: "tab_me_normal"), 
                              selectedImage: UIImage.base.image(name: "tab_me_selected"))

// 创建导航控制器
let homeNav = AICBaseNavigationController(rootViewController: homeVC)
let marketNav = AICBaseNavigationController(rootViewController: marketVC)
let meNav = AICBaseNavigationController(rootViewController: meVC)

// 设置子视图控制器
tabBarController.viewControllers = [homeNav, marketNav, meNav]
tabBarController.selectedIndex = 0

// 设置为根视图控制器
window.rootViewController = tabBarController
```

#### 3.3.2 自定义样式

`AICTabBarController`支持自定义样式：

```swift
// 设置标签栏外观
let tabBar = tabBarController.tabBar
tabBar.tintColor = UIColor.base.current.tabBarSelectedColor
tabBar.unselectedItemTintColor = UIColor.base.current.tabBarNormalColor
tabBar.backgroundColor = UIColor.base.current.tabBarBackgroundColor

// 设置标签栏阴影
tabBar.shadowImage = UIImage()
tabBar.backgroundImage = UIImage()

// 设置标签栏分割线
let lineView = UIView()
lineView.backgroundColor = UIColor.base.current.separatorColor
tabBar.addSubview(lineView)
lineView.snp.makeConstraints { make in
    make.top.left.right.equalToSuperview()
    make.height.equalTo(0.5)
}
```

### 3.4 AICSegmentedControl

`AICSegmentedControl`是自定义分段控制器，提供了统一的样式和行为。

#### 3.4.1 基本用法

```swift
// 创建分段控制器
let segmentedControl = AICSegmentedControl(items: ["日K".ticker.localized, "周K".ticker.localized, "月K".ticker.localized])
segmentedControl.selectedSegmentIndex = 0

// 设置点击事件
segmentedControl.addTarget(self, action: #selector(segmentChanged(_:)), for: .valueChanged)

// 添加到视图
view.addSubview(segmentedControl)
segmentedControl.snp.makeConstraints { make in
    make.top.equalTo(navigationBar.snp.bottom).offset(10)
    make.left.equalToSuperview().offset(15)
    make.right.equalToSuperview().offset(-15)
    make.height.equalTo(36)
}

// 处理点击事件
@objc private func segmentChanged(_ sender: AICSegmentedControl) {
    let index = sender.selectedSegmentIndex
    switch index {
    case 0:
        loadDailyKLine()
    case 1:
        loadWeeklyKLine()
    case 2:
        loadMonthlyKLine()
    default:
        break
    }
}
```

#### 3.4.2 自定义样式

`AICSegmentedControl`支持自定义样式：

```swift
// 设置颜色
segmentedControl.tintColor = UIColor.base.current.segmentedTintColor
segmentedControl.backgroundColor = UIColor.base.current.segmentedBackgroundColor

// 设置选中样式
segmentedControl.setTitleTextAttributes([
    NSAttributedString.Key.foregroundColor: UIColor.base.current.segmentedSelectedTextColor,
    NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14, weight: .medium)
], for: .selected)

// 设置未选中样式
segmentedControl.setTitleTextAttributes([
    NSAttributedString.Key.foregroundColor: UIColor.base.current.segmentedNormalTextColor,
    NSAttributedString.Key.font: UIFont.systemFont(ofSize: 14)
], for: .normal)

// 设置圆角
segmentedControl.layer.cornerRadius = 5
segmentedControl.clipsToBounds = true
```

## 4. 最佳实践

### 4.1 控制器最佳实践

1. **继承基础类**：所有视图控制器必须继承自`AICBaseViewController`或其子类
2. **生命周期管理**：在适当的生命周期方法中进行初始化和清理
3. **代码组织**：使用MARK注释分隔不同功能的代码
4. **内存管理**：使用weak self避免循环引用
5. **主题适配**：监听主题变化通知并更新UI

```swift
// 推荐的控制器结构
class TickerDetailViewController: AICBaseViewController {
    
    // MARK: - Properties
    private let marketKey: String
    private var marketModel: TickerMarketDetailModel?
    
    // MARK: - UI Components
    private lazy var priceLabel: AICBaseLabel = {
        let label = AICBaseLabel()
        label.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        label.textColor = UIColor.base.current.textColor
        return label
    }()
    
    // MARK: - Initialization
    init(marketKey: String) {
        self.marketKey = marketKey
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNotifications()
        loadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 视图即将显示时的操作
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // 取消网络请求
    }
    
    deinit {
        // 移除通知监听
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        title = "行情详情".ticker.localized
        view.backgroundColor = UIColor.base.current.backgroundColor
        
        // 添加子视图
        view.addSubview(priceLabel)
        
        // 设置约束
        priceLabel.snp.makeConstraints { make in
            make.top.equalTo(view.snp_safeTop).offset(20)
            make.centerX.equalToSuperview()
            make.height.equalTo(30)
        }
    }
    
    // MARK: - Notifications
    private func setupNotifications() {
        // 监听主题变化
        NotificationCenter.default.addObserver(self, 
                                              selector: #selector(themeDidChange), 
                                              name: NSNotification.Name(SSBThemeManagerShouldChangeTheme), 
                                              object: nil)
    }
    
    @objc private func themeDidChange() {
        // 更新UI颜色
        view.backgroundColor = UIColor.base.current.backgroundColor
        priceLabel.textColor = UIColor.base.current.textColor
    }
    
    // MARK: - Data
    private func loadData() {
        // 加载数据
        TickerNetworkManager.shared.requestMarketDetail(marketKey: marketKey) { [weak self] (model, error) in
            guard let self = self else { return }
            
            if let model = model {
                self.marketModel = model
                self.updateUI()
            } else {
                // 处理错误
                self.showToast(message: "加载失败，请重试")
            }
        }
    }
    
    // MARK: - UI Update
    private func updateUI() {
        guard let model = marketModel else { return }
        
        // 更新价格标签
        priceLabel.text = model.lastPrice
        
        // 根据涨跌幅设置颜色
        let change = model.degree24h.doubleValue
        priceLabel.textColor = change >= 0 ? UIColor.base.current.upColor : UIColor.base.current.downColor
    }
}
```

### 4.2 视图最佳实践

1. **继承基础类**：使用项目提供的基础视图类
2. **懒加载**：使用懒加载初始化视图组件
3. **主题适配**：支持主题切换
4. **复用视图**：创建可复用的自定义视图
5. **约束布局**：使用SnapKit进行自动布局

```swift
// 自定义可复用视图
class TickerPriceView: UIView {
    
    // MARK: - UI Components
    private lazy var priceLabel: AICBaseLabel = {
        let label = AICBaseLabel()
        label.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        label.textColor = UIColor.base.current.textColor
        return label
    }()
    
    private lazy var changeLabel: AICBaseLabel = {
        let label = AICBaseLabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor.base.current.upColor
        return label
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupNotifications()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupNotifications()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // 添加子视图
        addSubview(priceLabel)
        addSubview(changeLabel)
        
        // 设置约束
        priceLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview()
            make.right.equalToSuperview()
            make.height.equalTo(24)
        }
        
        changeLabel.snp.makeConstraints { make in
            make.top.equalTo(priceLabel.snp.bottom).offset(4)
            make.left.equalToSuperview()
            make.right.equalToSuperview()
            make.height.equalTo(18)
        }
    }
    
    // MARK: - Notifications
    private func setupNotifications() {
        // 监听主题变化
        NotificationCenter.default.addObserver(self, 
                                              selector: #selector(themeDidChange), 
                                              name: NSNotification.Name(SSBThemeManagerShouldChangeTheme), 
                                              object: nil)
    }
    
    @objc private func themeDidChange() {
        // 更新UI颜色
        updateTheme()
    }
    
    // MARK: - Public Methods
    func configure(price: String, change: Double) {
        priceLabel.text = price
        
        let changeText = change >= 0 ? "+\(change)%" : "\(change)%"
        changeLabel.text = changeText
        
        // 设置涨跌颜色
        changeLabel.textColor = change >= 0 ? UIColor.base.current.upColor : UIColor.base.current.downColor
    }
    
    // MARK: - Private Methods
    private func updateTheme() {
        priceLabel.textColor = UIColor.base.current.textColor
        
        // 保持涨跌颜色
        if let changeText = changeLabel.text, let change = Double(changeText.replacingOccurrences(of: "%", with: "").replacingOccurrences(of: "+", with: "")) {
            changeLabel.textColor = change >= 0 ? UIColor.base.current.upColor : UIColor.base.current.downColor
        }
    }
}
```

### 4.3 导航最佳实践

1. **统一导航栏**：使用`AICBaseNavigationBar`或`AICBaseNavigationController`
2. **返回按钮**：提供一致的返回按钮行为
3. **标题设置**：使用适当的标题和字体
4. **导航转场**：使用标准的导航转场方法
5. **深度管理**：避免过深的导航层级

```swift
// 导航控制器获取
let rootNavController = UIViewController.aic_getRootNavigationController()

// 页面跳转
func navigateToDetail(marketKey: String) {
    let detailVC = TickerDetailViewController(marketKey: marketKey)
    navigationController?.pushViewController(detailVC, animated: true)
}

// 返回上一页
@objc private func backAction() {
    navigationController?.popViewController(animated: true)
}

// 返回根页面
@objc private func homeAction() {
    navigationController?.popToRootViewController(animated: true)
}

// 模态展示
func presentSettings() {
    let settingsVC = SettingsViewController()
    let navController = AICBaseNavigationController(rootViewController: settingsVC)
    navController.modalPresentationStyle = .fullScreen
    present(navController, animated: true, completion: nil)
}

// 模态关闭
@objc private func dismissAction() {
    dismiss(animated: true, completion: nil)
}
```
# 1. AICoin工具类文档

AICoin-iOS项目中包含了丰富的工具类，用于处理字符串、日期和数字格式化等常见操作。本文档将详细介绍这些工具类的使用方法和示例代码。

## 1.1 字符串处理

AICoin-iOS项目中提供了多种字符串处理工具，主要通过String的扩展实现。

### 1.1.1 基础字符串操作

```swift
// 字符串截取
let str = "AICoin"
let firstChar = str[0]  // "A"
let subStr1 = str.substring(fromIndex: 2)  // "Coin"
let subStr2 = str.substring(toIndex: 3)  // "AIC"
let rangeStr = str[1..<4]  // "ICo"

// 本地化字符串
let localizedStr = "ticker_title".ticker.localized  // 使用Ticker模块本地化
let baseLocalizedStr = "common_confirm".base.localized  // 使用基础模块本地化
```

### 1.1.2 字符串搜索和高亮

```swift
// 搜索分词
let searchText = "BTC/USDT"
let tokens = searchText.searchTokenize  // 返回 ["BTC", "USDT", "/"]

// 高亮显示
let text = "BTC价格上涨"
let keyWords = ["BTC"]
let highlightColor = UIColor.red
let attributedString = text.highlight(keyWords: keyWords, highlightColor: highlightColor)
```

### 1.1.3 字符串格式化

```swift
// 手机号格式化 (例如: 138 1234 5678)
let phoneNumber = "13812345678"
let formattedPhone = phoneNumber.phoneNumParseString()

// 提取最长数字
let mixedString = "abc123def4567"
if let longestNumber = mixedString.longestNumber() {
    print(longestNumber)  // "4567"
}

// 字符串转Double数组
let arrayString = "[1738339200,80000,1738723122,100000]"
if let doubleArray = arrayString.toDoubleArray() {
    print(doubleArray)  // [1738339200.0, 80000.0, 1738723122.0, 100000.0]
}
```

## 1.2 日期处理

AICoin-iOS项目使用Date扩展和SwiftDate库处理日期和时间相关操作。

### 1.2.1 时间戳操作

```swift
// 获取当前时间戳
let timestamp = Date.getNowTimeStamp()  // 返回10位时间戳

// 获取当前时间字符串
let currentTimeString = Date.getNowTimeString(dateFormat: "yyyy-MM-dd HH:mm:ss")

// 时间戳转换为时间字符串
let dateString = Date.getTimeString(timeStamp: 1672531200, dateFormat: "yyyy-MM-dd")  // "2023-01-01"
```

### 1.2.2 日期转换

```swift
// 日期字符串转Date对象
let date = Date.getDate(timeString: "2023-01-01 12:00:00", dateFormat: "yyyy-MM-dd HH:mm:ss")

// 日期字符串转时间戳
let timestamp = Date.getTimeStamp(timeString: "2023-01-01 12:00:00", dateFormat: "yyyy-MM-dd HH:mm:ss")

// Date对象转字符串
let dateString = date.getStringTime(dateFormat: "yyyy年MM月dd日")
```

### 1.2.3 相对时间显示

```swift
// 相对日期显示（今天、昨天、日期）
let relativeDate = Date.relativeDateString(from: 1672531200)  // 根据日期返回"今天"、"昨天"或具体日期

// 转换为"刚刚"、"x分钟前"、"x小时前"等格式
let relativeTimeString = Date.convertTimeToString(1672531200)
```

### 1.2.4 日期组件获取

```swift
// 获取日期的年、月、日、时、分、秒
let date = Date()
let (year, month, day, hour, minute, second) = date.getTime()
```

### 1.2.5 日期计算

```swift
// 计算两个日期之间的天数
let days = TickerDataFormatHelper.calenderTime(endTime: 1672531200)
```

## 1.3 数字格式化

AICoin-iOS项目提供了多种数字格式化工具，用于处理价格、百分比等数值的显示。

### 1.3.1 基础数字格式化

```swift
// 保留指定小数位
let formattedNumber = TickerDataFormatHelper.transformStr(str: "123.456789", num: 2)  // "123.46"

// 添加千位分隔符
let numberWithSeparator = TickerDataFormatHelper.requestNumFormatter(data: "1234567.89")  // "1,234,567.89"

// 格式化带正负号的数字
let signedNumber = TickerMethod.SignNumber(10.5)  // "+10.5"
```

### 1.3.2 特定场景的数字格式化

```swift
// 格式化百分比
let percentString = TickerDataFormatHelper.setRate(str: "0.1256")  // "12.56%"

// 格式化价格（根据数值大小自动调整小数位数）
let priceString = TickerMethod.formatWithUnit(value: "1234.56", unit: true)
// 返回格式化后的价格和单位，例如 (value: "1.23", unit: "K")

// 全球指数价格格式化
let globalPrice = TickerDataFormatHelper.requsetGlobalPrice(price: "12.3456")  // "12.35"
```

### 1.3.3 使用NumberFormatter

AICoin-iOS项目使用AICCacheManager缓存NumberFormatter实例，提高性能：

```swift
// 获取缓存的NumberFormatter
let formatter = AICCacheManager.share.numberFormatter(withId: "myFormatter") { formatter in
    formatter.numberStyle = .decimal
    formatter.roundingMode = .halfEven
    formatter.minimumFractionDigits = 2
    formatter.maximumFractionDigits = 2
    formatter.usesGroupingSeparator = true
}

// 使用formatter格式化数字
let formattedString = formatter.string(for: Decimal(string: "1234.56"))  // "1,234.56"
```

### 1.3.4 数值舍入和精度控制

```swift
// 四舍五入到指定小数位
let roundedValue = 0.123456789.rounded(toPlaces: 4)  // 0.1235

// 四舍五入并返回固定小数位的字符串
let roundedString = 0.123456789.roundedString(toPlaces: 4)  // "0.1235"
```

## 1.4 定时器工具

AICoin-iOS项目提供了TimerHelper类用于管理定时器：

```swift
// 启动定时器（使用selector回调）
TimerHelper.shareInstance.startTimerWithIdentifier("updatePrice",
                                                 interval: 3.0,
                                                 target: self,
                                                 selector: #selector(updatePrice))

// 启动定时器（使用闭包回调）
TimerHelper.shareInstance.startTimer(with: "updateChart",
                                   interval: 5.0) { [weak self] in
    self?.updateChart()
}

// 停止定时器
TimerHelper.shareInstance.stopTimerWithIdentifier("updatePrice")

// 暂停定时器
TimerHelper.shareInstance.pause(identifier: "updateChart")

// 恢复定时器
TimerHelper.shareInstance.resume(identifier: "updateChart")

// 停止所有定时器
TimerHelper.shareInstance.stopAllTimers()
```

## 1.5 UIViewController扩展

AICoin-iOS项目为UIViewController提供了丰富的扩展方法，主要用于页面跳转和登录验证等常用功能。

### 1.5.1 Storyboard加载

UIViewController扩展实现了ViewControllerStoryboardLoadable协议，提供了从Storyboard加载视图控制器的便捷方法：

```swift
// 从Storyboard加载视图控制器
let vc = InformationDetailViewController.fromStoryboard(storyboardName: "News", identifier: "InformationDetailViewController")

// 使用默认identifier（类名）
let vc = MyViewController.fromStoryboard(storyboardName: "Main")
```

### 1.5.2 页面跳转方法

#### 跳转到行情详情页

```swift
// 跳转到行情详情页
jumpToTickerDetailVC(firstKey: "binance_btcusdt", listKeys: ["binance_btcusdt", "okex_btcusdt"])

// 跳转到行情详情页并设置指标
jumpToTickerDetailSetIndicatorVC(firstKey: "binance_btcusdt", indicator: "MACD")
```

#### 跳转到全球指数页面

```swift
// 跳转到全球指数页面
jumpToGlobalIndexVC(coinKey: "bitcoin", coinShow: "比特币", selectedType: .analysis)
```

#### 跳转到指数详情页

```swift
// 跳转到指数详情页
jumpToIndexDetailVC(indexModel: indexModel)
```

#### 跳转到Web页面

```swift
// 跳转到Web页面
jumpToWebViewController("https://www.aicoin.com")
```

#### 跳转到新闻详情页

```swift
// 跳转到新闻详情页
jumpToNewsDetailVC(articelId: "123456")
```

#### 跳转到关于我们页面

```swift
// 跳转到关于我们页面
jumpToAboutUsVC()
```

### 1.5.3 登录相关方法

#### 跳转到登录页面

```swift
// 跳转到登录页面
jumpToLoginViewController {
    // 登录成功后的回调
    print("登录成功")
}

// 检查登录状态，如果已登录则直接执行回调
jumpToLoginViewController {
    // 如果已登录，直接执行此回调
    self.performLoginRequiredAction()
}
```

#### 自动登录验证

```swift
// 自动登录验证（显示登录提示弹窗）
autoLogin {
    // 登录成功后执行的操作
    self.performPrivilegedAction()
}

// 聊天室自动登录（直接跳转登录页面）
chatRoomAutoLogin {
    // 登录成功后进入聊天室
    self.enterChatRoom()
}

// 指数页面自动登录（自定义提示信息）
autoIndexLogin(str: "查看指数详情需要登录") {
    // 登录成功后查看指数详情
    self.showIndexDetail()
}
```

### 1.5.4 预警页面跳转

```swift
// 跳转到预警设置页面
jumpToTickerWaringVC(
    marketModel: marketModel,
    subModel: subModel,
    editModel: editModel,
    selectIndex: 0,
    klineModel: klineModel,
    isPopToast: false,
    launchScreen: false,
    scriptID: 0,
    track: "homepage"
)
```

### 1.5.5 内部跳转方法

#### p_pushToVC方法

这是一个内部使用的跳转方法，会自动设置`hidesBottomBarWhenPushed = true`：

```swift
// 内部跳转方法（推荐在扩展内部使用）
func customJumpMethod() {
    let vc = MyViewController()
    self.p_pushToVC(vc)  // 自动隐藏底部TabBar
}
```

### 1.5.6 使用建议

1. **统一跳转方式**：使用扩展提供的跳转方法，保持应用内跳转的一致性
2. **登录验证**：对于需要登录的功能，使用`autoLogin`方法进行统一的登录验证
3. **参数传递**：跳转方法已经封装了常用的参数传递逻辑，直接使用即可
4. **错误处理**：扩展方法内部已经处理了常见的错误情况，如URL验证等

### 1.5.7 注意事项

- 所有跳转方法都会自动设置`hidesBottomBarWhenPushed = true`
- 登录相关方法会自动检查当前登录状态
- Web页面跳转会根据应用配置决定是内部打开还是外部浏览器打开
- 部分方法使用了`@objc`标记，支持Objective-C调用

以上是AICoin-iOS项目中常用的字符串处理、日期处理、数字格式化和UIViewController扩展工具类的使用方法。这些工具类可以帮助开发者更高效地处理各种数据格式化需求和页面跳转逻辑。

# 1. AICoin数据绑定文档

AICoin-iOS项目使用ObservableValue作为主要的数据绑定方案。本文档将详细介绍ObservableValue的使用方法和示例代码。

## 1.1 ObservableValue数据绑定

ObservableValue是AICoin-iOS项目中使用的一种轻量级数据绑定方案，通过属性观察方法通知所有观察者数据变化。它简洁直观，特别适合UI更新和数据同步场景。

### 1.1.1 基本概念

ObservableValue是一个泛型类，可以包装任何类型的值，并在值发生变化时通知所有订阅者。它的核心功能包括：

1. 创建可观察的值
2. 订阅值的变化
3. 更新值并自动通知订阅者
4. 管理订阅的生命周期

### 1.1.2 创建可观察值

有两种方式可以创建ObservableValue：

#### 直接创建ObservableValue实例

```swift
// 创建一个包含初始值的ObservableValue
let price = ObservableValue<Double>(default: 0.0)
let marketModels = ObservableValue<[TickerMarketListModel]>(default: [])
```

#### 使用@ObservableObj属性包装器

```swift
// 使用属性包装器创建可观察属性
@ObservableObj
var price: Double = 0.0

@ObservableObj
var isSelected: Bool = false
```

### 1.1.3 订阅数据变化

订阅ObservableValue的变化，以便在值发生变化时执行相应的操作：

```swift
// 直接创建的ObservableValue订阅
price.subscribe { [weak self] newPrice in
    // 处理价格变化
    self?.updatePriceLabel(with: newPrice)
}.disposed(by: disposeBag)

// 使用属性包装器创建的ObservableValue订阅
$price.subscribe { [weak self] newPrice in
    // 处理价格变化
    self?.updatePriceLabel(with: newPrice)
}.disposed(by: disposeBag)
```

ObservableValue提供了两种订阅方法：
- `subscribe`: 仅在值发生变化时通知订阅者
- `subscribeImmediately`: 订阅后立即通知一次，之后在值发生变化时再通知

```swift
// 订阅并立即接收当前值
marketModels.subscribeImmediately { [weak self] models in
    // 立即处理当前数据，之后在数据变化时也会调用
    self?.refreshUI(with: models)
}.disposed(by: disposeBag)
```

### 1.1.4 更新数据

更新ObservableValue的值，会自动通知所有订阅者：

```swift
// 直接创建的ObservableValue更新
price.value = 42.0  // 所有订阅者都会收到通知

// 使用属性包装器创建的ObservableValue更新
price = 42.0  // 所有订阅者都会收到通知
```

### 1.1.5 管理订阅生命周期

为了避免内存泄漏，需要正确管理订阅的生命周期。AICoin-iOS项目使用DisposeBag来管理订阅：

```swift
// 创建DisposeBag
private let disposeBag = DisposeBag()

// 将订阅添加到DisposeBag中
marketModels.subscribe { [weak self] models in
    self?.refreshUI(with: models)
}.disposed(by: disposeBag)

// 当DisposeBag被释放或调用dispose()方法时，所有订阅都会被取消
```

在视图控制器的deinit方法中，DisposeBag会自动释放，从而取消所有订阅。也可以手动调用dispose()方法取消订阅：

```swift
// 手动取消所有订阅
disposeBag.dispose()
```

### 1.1.6 实际应用示例

#### 示例1：行情价格更新

```swift
// ViewModel中定义可观察属性
class TickerViewModel {
    let marketModels = ObservableValue<[TickerMarketListModel]>(default: [])

    func fetchData() {
        // 获取数据后更新ObservableValue
        AICTickerRequestOperation.requestMarketList { [weak self] models in
            self?.marketModels.value = models
        }
    }
}

// ViewController中订阅数据变化
class TickerViewController: UIViewController {
    private let viewModel = TickerViewModel()
    private let disposeBag = DisposeBag()

    override func viewDidLoad() {
        super.viewDidLoad()

        // 订阅市场数据变化
        viewModel.marketModels.subscribe { [weak self] models in
            self?.tableView.reloadData()
        }.disposed(by: disposeBag)

        // 获取数据
        viewModel.fetchData()
    }
}
```

#### 示例2：悬浮窗数据绑定

```swift
// 悬浮窗设置数据模型
class TickerPipSettingCacheModel {
    static let shared = TickerPipSettingCacheModel()

    // 唯一数据源
    lazy var marketModels: ObservableValue<[TickerMarketListModel]> = ObservableValue(default: AICTickerDataBase.share.fetchTickerMarketListModelWithMarketListKeys(keys))

    private let disposeBag = DisposeBag()

    // 绑定更改
    init() {
        marketModels.subscribe { [weak self] models in
            let keys = models.map { $0.marketListKey }
            self?.keys = keys
        }.disposed(by: disposeBag)
    }
}

// 悬浮窗视图中订阅数据变化
class FloatingWindow: UIWindow {
    private let viewModel = TickerPipSettingCacheModel.shared
    private let disposeBag = DisposeBag()

    override init(frame: CGRect) {
        super.init(frame: frame)

        // 订阅市场数据变化
        viewModel.marketModels.subscribe { [weak self] models in
            self?.transform(num: models.count)
            self?.refreshListView(by: models)
        }.disposed(by: disposeBag)
    }
}
```

### 1.1.7 与KVO的结合使用

在某些情况下，AICoin-iOS项目也使用KVO结合DisposeBag来实现数据绑定：

```swift
// 使用KVO监听属性变化
let dis = viewModel.addObserverBlockReturnDis(forKeyPath: subKey, block: { [weak self] (_model, _old_value, _new_value) in
    guard let self = self else {
        return
    }

    let value = viewModel.getData(key: cacheKey)
    let str = type.formatter(with: value)

    AICSwiftTool.runonMainQueue {
        self.subValue2Label.text = str
    }
})

// 将KVO订阅添加到DisposeBag中
self.disposesBag?.insert(dis)
```

### 1.1.8 优势

1. **简洁直观**：ObservableValue的API设计简洁，使用直观
2. **类型安全**：使用泛型实现，提供类型安全的数据绑定
3. **内存管理**：通过DisposeBag和weak引用避免内存泄漏
4. **轻量级**：不需要引入大型第三方库，减少项目依赖
5. **易于集成**：可以与现有代码无缝集成，不需要大规模重构

ObservableValue是AICoin-iOS项目中推荐的数据绑定方案，适用于UI更新和数据同步场景。它提供了简单而强大的响应式编程能力，满足项目的大部分数据绑定需求。

# AICoin-iOS开发规范与最佳实践

本文档整合了AICoin iOS项目的编码规范和最佳实践经验，帮助开发人员更高效地进行开发工作，确保代码的一致性和可维护性。

## 一、项目架构与组织

### 1.1 项目目录结构

项目使用模块化组织方式，按照功能模块划分目录结构：

```
AICoin-iOS/
├── AICoin/                # 主项目目录
│   ├── Module/            # 业务模块
│   │   ├── Base/          # 基础组件
│   │   ├── CandleChart/   # K线图表模块
│   │   ├── Content/       # 内容模块（资讯、快讯、动态）
│   │   ├── HomePage/      # 首页模块
│   │   ├── Login/         # 登录模块
│   │   ├── Me/            # 个人中心模块
│   │   ├── Ticker/        # 行情模块
│   │   ├── Trade/         # 交易模块
│   │   └── 自选/          # 自选模块
│   ├── Resources/         # 资源文件
│   │   ├── Images/        # 图片资源
│   │   ├── Fonts/         # 字体资源
│   │   └── Sounds/        # 音频资源
│   ├── Supporting Files/  # 配置文件
│   │   ├── Pro/           # 正式环境配置
│   │   ├── Test/          # 测试环境配置
│   │   └── OverSea/       # 海外版配置
│   └── Vendor/            # 第三方库
├── AICoinWigetKit/        # 小组件模块
├── Docs/                  # 文档
└── Pods/                  # CocoaPods依赖
```

### 1.2 模块内部结构

#### 1.2.1 MVC架构模块

```
Module/Ticker/
├── Model/                 # 数据模型
│   ├── TickerModel.swift
│   └── DB/                # 数据库相关
├── NetManager/            # 网络请求管理
│   └── TickerNetworkManager.swift
├── 行情列表/              # 功能子模块
│   ├── HTTP控制器/        # 控制器
│   └── View/              # 视图组件
├── 行情详情/              # 功能子模块
│   ├── Model/             # 子模块模型
│   ├── View/              # 子模块视图
│   └── 报价/              # 子功能
├── Resource/              # 模块资源
│   └── TickerImage.xcassets/
└── Tools/                 # 工具类
    └── Category/          # 分类扩展
```

#### 1.2.2 MVVM架构模块（推荐）

```
Module/自选/地址/          # 自选-地址模块
├── Model/                 # 数据模型
│   └── AddressModel.swift
├── View/                  # 视图组件
│   └── AddressCell.swift
├── ViewModel/             # 视图模型
│   └── AddressViewModel.swift
└── AddressViewController.swift
```

新开发的功能模块应优先采用MVVM架构，以提高代码的可维护性和可测试性。

### 1.3 架构模式选择

#### 1.3.1 MVC架构

适用于简单的业务逻辑：

```swift
// Model
struct TickerModel: Codable {
    let id: String
    let name: String
    let price: Double
    let change: Double
}

// Controller
class TickerViewController: AICBaseViewController {
    private lazy var tableView = UITableView()
    private var tickers: [TickerModel] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
    }
    
    private func loadData() {
        AICBaseHttpManager.shared.post("/api/v1/ticker/list", parameters: nil, progress: nil, success: { [weak self] task, response in
            // 处理响应
        }, failure: { [weak self] task, error in
            // 处理错误
        })
    }
}
```

#### 1.3.2 MVVM架构（推荐）

适用于复杂的业务逻辑和数据绑定：

```swift
// ViewModel
class AddressViewModel {
    private(set) var addresses: [AddressModel] = []
    var didLoad: (() -> Void)?
    
    func requestAddressList() {
        AICBaseHttpManager.shared.post("/api/v1/address/list", parameters: nil, progress: nil, success: { [weak self] task, response in
            guard let self = self else { return }
            // 处理响应数据
            self.didLoad?()
        }, failure: { [weak self] task, error in
            // 处理错误
        })
    }
}

// View (ViewController)
class AddressViewController: AICBaseViewController {
    private let viewModel = AddressViewModel()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        bindViewModel()
        loadData()
    }
    
    private func bindViewModel() {
        viewModel.didLoad = { [weak self] in
            self?.tableView.reloadData()
        }
    }
}
```

## 二、命名规范

### 2.1 命名风格

- **类名**：使用大驼峰命名法（UpperCamelCase）
  ```swift
  class AICTickerViewController: AICBaseViewController { }
  ```

- **变量和方法**：使用小驼峰命名法（lowerCamelCase）
  ```swift
  var tickerName: String
  func requestTickerData() { }
  ```

- **常量**：使用小驼峰命名法
  ```swift
  let maxRetryCount = 3
  static let shared = AICHttpManager()
  ```

- **枚举**：枚举类型使用大驼峰命名法，枚举值使用小驼峰命名法
  ```swift
  enum AICNetworkEnvironment {
      case production
      case testing
      case proProduction
      case proTesting
  }
  ```

### 2.2 文件命名规范

- **控制器文件**：`功能+ViewController.swift`
- **视图文件**：`功能+View.swift` 或 `功能+Cell.swift`
- **模型文件**：`功能+Model.swift`
- **视图模型文件**：`功能+ViewModel.swift`
- **扩展文件**：`类型+Feature.swift`
- **工具类文件**：`功能+Helper/Manager/Tool.swift`

### 2.3 语义化命名

命名应当清晰表达含义，避免使用缩写：

```swift
// 推荐
func fetchUserInfo() { }
var formattedPrice: String { }

// 不推荐
func getData() { }
var fmtPrice: String { }
```

### 2.4 统一命名风格

同一概念在不同地方应使用相同的命名：

```swift
// 推荐
struct Ticker {
    let tickerId: String
    let tickerName: String
}

class TickerViewController {
    var tickerId: String
    var tickerName: String
}
```

## 三、代码组织与结构

### 3.1 基类使用

所有视图控制器必须继承自`AICBaseViewController`：

```swift
// 推荐
class AICHomeViewController: AICBaseViewController {
    // 实现代码
}

// 不推荐
class HomeViewController: UIViewController {
    // 实现代码
}
```

### 3.2 类内部组织

类内部代码按以下顺序组织：

```swift
class AICExampleViewController: AICBaseViewController {
    
    // MARK: - Properties
    private let viewModel = ExampleViewModel()
    private let disposeBag = DisposeBag()
    
    // MARK: - UI Components
    private lazy var tableView: UITableView = {
        let tableView = UITableView()
        tableView.delegate = self
        tableView.dataSource = self
        return tableView
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindData()
        loadData()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // UI设置代码
    }
    
    // MARK: - Data Binding
    private func bindData() {
        // 数据绑定代码
    }
    
    // MARK: - Private Methods
    private func loadData() {
        // 数据加载代码
    }
}

// MARK: - UITableViewDelegate
extension AICExampleViewController: UITableViewDelegate {
    // 实现代理方法
}

// MARK: - UITableViewDataSource
extension AICExampleViewController: UITableViewDataSource {
    // 实现数据源方法
}
```

### 3.3 获取根控制器

获取根控制器应使用统一方法：

```swift
// 推荐
let rootNavController = UIViewController.aic_getRootNavigationController()
rootNavController.pushViewController(viewController, animated: true)
```

## 四、组件选择指南

### 4.1 UI组件选择

| 需求 | 推荐组件 | 说明 |
|------|----------|------|
| 基础视图控制器 | AICBaseViewController | 提供统一的导航栏、状态栏和主题适配 |
| 列表视图控制器 | AICBaseTableViewController | 提供统一的空视图和刷新控件 |
| 分段控制器 | AICSegmentedControl | 支持自定义样式和主题适配 |
| 开关控件 | AICSwitch | 统一的视觉风格，支持主题切换 |
| 弹窗 | AICBaseAlertController | 统一的弹窗样式和交互方式 |
| 下拉刷新 | AICRefresh | 封装MJRefresh，提供统一的刷新体验 |
| 空视图 | DZNEmptyDataSet | 提供统一的空数据展示 |
| 图片浏览 | YYPhotoBrowseView | 高性能的图片浏览体验 |

### 4.2 布局工具选择

- **优先使用SnapKit**进行布局，不再使用Storyboard或XIB
- 复杂布局可以考虑使用UIStackView简化代码
- 使用AICFitPlus进行屏幕适配，处理不同设备的布局差异

```swift
// 推荐的布局方式
view.addSubview(titleLabel)
titleLabel.snp.makeConstraints { make in
    make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(16)
    make.left.right.equalToSuperview().inset(20)
}
```

### 4.3 数据模型选择

- **纯Swift类**：优先使用DataDecodable
- **与OC交互的类**：使用YYModel并添加@objcMembers
- **复杂或不确定结构的JSON**：使用SwiftyJSON

```swift
// 纯Swift类推荐使用DataDecodable
struct TickerModel: DataDecodable {
    var price: String = ""
    var change: String = ""
    
    static func aicModel(with json: [String: Any]) -> TickerModel {
        var model = TickerModel()
        model.price = json["price"] as? String ?? ""
        model.change = json["change"] as? String ?? ""
        return model
    }
}
```

### 4.4 数据存储选择

- **数据库存储**：使用WCDB进行数据持久化
- **简单配置**：使用UserDefaults
- **缓存管理**：使用AICCacheManager
- **敏感数据**：存储在钥匙串中

### 4.5 数据绑定选择

- **数据绑定**：使用ObservableValue
- **属性包装器**：使用@ObservableObj

## 五、网络请求规范

### 5.1 基本使用

所有网络请求都应通过`AICBaseHttpManager.shared`发起：

```swift
AICBaseHttpManager.shared.post("/api/v1/endpoint",
                               parameters: params,
                               progress: nil,
                               success: { [weak self] task, response in
    guard let self = self else { return }
    // 处理成功响应
}, failure: { [weak self] task, error in
    guard let self = self else { return }
    // 处理错误
})
```

### 5.2 请求封装

为每个业务模块创建专门的网络请求管理类：

```swift
class TickerNetworkManager {
    static let shared = TickerNetworkManager()
    
    func requestTickerList(completion: @escaping ([TickerModel]?, Error?) -> Void) {
        AICBaseHttpManager.shared.post("/api/v1/ticker/list",
                                      parameters: nil,
                                      progress: nil,
                                      success: { task, response in
            let json = JSON(response ?? "")
            if json["success"].boolValue {
                let dataArray = json["data"].arrayValue
                let models = dataArray.compactMap { TickerModel.aicModel(with: $0.dictionaryObject ?? [:]) }
                completion(models, nil)
            } else {
                completion(nil, NSError(domain: "APIError",
                                      code: json["errorCode"].intValue,
                                      userInfo: nil))
            }
        }, failure: { task, error in
            completion(nil, error)
        })
    }
}
```

### 5.3 响应处理

使用SwiftyJSON处理响应数据：

```swift
let json = JSON(response ?? "")
if json["success"].boolValue {
    // 请求成功
    let data = json["data"]
    // 处理数据
} else {
    // 请求失败
    let errorCode = json["errorCode"].intValue
    let errorMessage = json["error"].stringValue
    // 处理错误
}
```

### 5.4 缓存策略

合理使用缓存减少网络请求：

```swift
// 缓存网络响应数据，设置合理的过期时间
AICNetworkCache.shared.cache(key: cacheKey, value: model, expireSeconds: 60 * 5)  // 缓存5分钟

// 获取缓存数据
if let cachedModel: TickerModel = AICNetworkCache.shared.get(for: cacheKey) {
    // 使用缓存数据
    self.updateUI(with: cachedModel)
}
```

### 5.5 WebSocket管理

使用TickerSubscribeManager管理WebSocket订阅：

```swift
// 使用TickerSubscribeManager管理WebSocket订阅
let observation = TickerSubscribeManager.shared.subscribe(key) { [weak self] (model) in
    guard let self = self else { return }
    // 处理数据更新
}

// 保持对observation的引用，当不再需要时会自动取消订阅
self.observation = observation
```

## 六、内存管理

### 6.1 避免循环引用

在闭包中使用`[weak self]`或`[unowned self]`避免循环引用：

```swift
// 使用[weak self]
someAsyncOperation { [weak self] result in
    guard let self = self else { return }
    self.updateUI(with: result)
}

// 短生命周期闭包可使用[unowned self]
UIView.animate(withDuration: 0.3) { [unowned self] in
    self.view.alpha = 0
}
```

### 6.2 代理使用弱引用

代理属性应声明为弱引用：

```swift
weak var delegate: MyProtocol?
```

### 6.3 资源管理

- 大型资源及时释放
- 使用autoreleasepool处理临时大量对象
- 监听内存警告通知，及时释放非必要资源

```swift
// 处理大量临时对象
autoreleasepool {
    for i in 0..<10000 {
        let tempObject = createTemporaryObject()
        processObject(tempObject)
    }
}

// 监听内存警告
NotificationCenter.default.addObserver(self, selector: #selector(handleMemoryWarning), name: UIApplication.didReceiveMemoryWarningNotification, object: nil)

@objc func handleMemoryWarning() {
    // 释放非必要资源
    imageCache.removeAllObjects()
}
```

### 6.4 取消请求

在视图控制器的`viewWillDisappear`方法中取消未完成的网络请求：

```swift
class MyViewController: AICBaseViewController {
    var dataTask: URLSessionDataTask?
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        dataTask?.cancel()
    }
}
```

## 七、数据库使用规范

### 7.1 WCDB使用规范

项目使用WCDB.Swift进行数据持久化，遵循以下规范：

#### 数据库模型定义

```swift
final class MessageModel: TableCodable {
    var messageID: Int = 0
    var content: String = ""
    var senderID: String = ""
    var timestamp: Int = 0

    enum CodingKeys: String, CodingTableKey {
        typealias Root = MessageModel
        static let objectRelationalMapping = TableBinding(CodingKeys.self)
        case messageID
        case content
        case senderID
        case timestamp
    }
}
```

#### 复杂数据类型处理

```swift
extension MessageModel {
    // 字典数据的计算属性
    var data: [String: Any] {
        get {
            if let jsonData = dataJSON.data(using: .utf8),
               let dict = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any] {
                return dict
            }
            return [:]
        }
        set {
            if let jsonData = try? JSONSerialization.data(withJSONObject: newValue),
               let jsonString = String(data: jsonData, encoding: .utf8) {
                dataJSON = jsonString
            }
        }
    }
}
```

#### 数据库管理器

```swift
class MessageDBManager {
    static let shared = MessageDBManager()
    private let database: Database
    private let tableName = "messages"
    
    private init() {
        let path = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first! + "/messages.db"
        database = Database(withPath: path)
        setupDatabase()
    }
    
    private func setupDatabase() {
        do {
            try database.create(table: tableName, of: MessageModel.self)
        } catch {
            print("创建表失败: \(error)")
        }
    }
    
    // CRUD操作
    func insert(message: MessageModel) -> Bool {
        do {
            try database.insert(objects: message, intoTable: tableName)
            return true
        } catch {
            print("插入失败: \(error)")
            return false
        }
    }
}
```

## 八、UI布局与样式

### 8.1 布局方式

推荐使用Auto Layout实现UI布局，可借助SnapKit简化代码：

```swift
priceLabel.snp.makeConstraints { make in
    make.top.equalTo(nameLabel.snp.bottom).offset(8)
    make.left.equalToSuperview().offset(16)
    make.right.equalToSuperview().offset(-16)
}
```

### 8.2 统一边距

使用统一的边距常量：

```swift
private struct Layout {
    static let standardMargin: CGFloat = 16.0
    static let smallMargin: CGFloat = 8.0
    static let largeMargin: CGFloat = 24.0
}
```

### 8.3 设备适配

使用safeArea适配不同设备：

```swift
view.addSubview(contentView)
contentView.snp.makeConstraints { make in
    make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
    make.left.right.equalToSuperview()
    make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
}
```

## 九、资源管理

### 9.1 颜色使用

所有颜色定义必须使用项目封装的主题管理系统：

```swift
// 推荐 - 使用预定义主题颜色
label.textColor = UIColor.baseCurrentTextColor
view.backgroundColor = UIColor.baseCurrentBgColor

// 使用DynamicHelper创建动态颜色
let borderColor = DynamicHelper.themeColor(day: 0x333333, night: 0xC3C7D9)
```

### 9.2 图片使用

图片应根据所属模块使用对应的命名空间：

```swift
// 行情模块图片
let tickerImage = UIImage.ticker.image(name: "ticker_icon")

// 通用/基础图片
let logoImage = UIImage.base.image(name: "app_logo")
```

### 9.3 多语言支持

所有展示给用户的字符串必须支持多语言：

```swift
// 推荐
titleLabel.text = "行情".base.localized

// 格式化字符串
let priceText = String(format: "price_format".base.localized, price)
```

## 十、错误处理

### 10.1 避免强制解包

避免使用`!`强制解包，应使用可选绑定或guard语句：

```swift
// 推荐
guard let price = ticker.price else {
    // 处理nil情况
    return
}

// 不推荐
let price = ticker.price!
```

### 10.2 使用Result类型

使用Result类型处理可能失败的操作：

```swift
func fetchUserProfile(completion: @escaping (Result<UserProfile, Error>) -> Void) {
    networkService.request(.userProfile) { result in
        switch result {
        case .success(let response):
            do {
                let profile = try JSONDecoder().decode(UserProfile.self, from: response.data)
                completion(.success(profile))
            } catch {
                completion(.failure(error))
            }
        case .failure(let error):
            completion(.failure(error))
        }
    }
}
```

### 10.3 网络错误处理

网络请求错误处理应包括网络连接检查：

```swift
func handleAPIError(_ error: Error) {
    if !AICHttpManager.shared.isReachable {
        showToast(message: "网络连接不可用，请检查网络设置")
    } else {
        showToast(message: error.localizedDescription)
    }
}
```

## 十一、性能优化

### 11.1 网络优化

1. **合理使用缓存**
   - 使用AICNetworkCache缓存网络响应数据
   - 为不同类型的数据设置合理的缓存过期时间

2. **异步处理**
   - 大量数据处理使用子线程回调
   - 使用AICSwiftTool提供的线程管理方法

```swift
// 在全局队列异步执行耗时操作
AICSwiftTool.runonGlobalQueueAsync {
    let processedData = self.processData(rawData)
    
    // 在主线程更新UI
    AICSwiftTool.runonMainQueue {
        self.updateUI(with: processedData)
    }
}
```

### 11.2 UI性能优化

1. **列表优化**
   - 使用复用机制（UITableView/UICollectionView）
   - 避免在cellForRowAt方法中执行耗时操作
   - 使用预计算的行高提高滚动性能

2. **图片加载优化**
   - 使用YYWebImageManager异步加载图片
   - 设置合理的图片缓存策略

```swift
// 异步加载图片并缓存
imageView.yy_setImage(with: URL(string: imageUrl), placeholder: placeholderImage)
```

3. **主线程优化**
   - 避免主线程进行耗时操作
   - 使用异步方法更新UI

## 十二、注释规范

### 12.1 代码注释

使用`//`添加单行注释，使用`/// `添加文档注释：

```swift
/// 获取行情数据
/// - Parameters:
///   - tickerId: 行情ID
///   - completion: 完成回调，返回行情模型和错误信息
func requestTickerData(tickerId: String, completion: @escaping (TickerModel?, Error?) -> Void) {
    // TODO: 添加缓存支持
    
    // 构建请求参数
    let params = ["id": tickerId]
    
    // 发送请求
    AICBaseHttpManager.shared.post("/api/ticker", parameters: params, progress: nil, success: { (task, response) in
        // 解析响应数据
        let model = TickerModel.aicModel(with: response as? [String: Any] ?? [:])
        completion(model, nil)
    }, failure: { (task, error) in
        completion(nil, error)
    })
}
```

### 12.2 MARK注释

使用`// MARK:`分割代码区域：

```swift
// MARK: - Properties
// MARK: - Lifecycle Methods
// MARK: - Private Methods
```

## 十三、Swift最佳实践

### 13.1 使用Swift现代特性

#### 类型推断

当类型可以被编译器推断时，可以省略类型声明：

```swift
// 推荐
let message = "Hello"
let numbers = [1, 2, 3]

// 推荐（类型不明确时声明）
let price: Double = 29.99
```

#### 计算属性

使用计算属性代替简单的方法：

```swift
// 推荐
var formattedPrice: String {
    return String(format: "%.2f", price)
}

// 不推荐
func getFormattedPrice() -> String {
    return String(format: "%.2f", price)
}
```

### 13.2 函数式编程

使用map、filter、reduce等高阶函数处理集合：

```swift
// 推荐
let names = users.map { $0.name }
let activeUsers = users.filter { $0.isActive }
let totalBalance = accounts.reduce(0) { $0 + $1.balance }
```

### 13.3 并发和异步编程

合理使用GCD进行异步操作：

```swift
// 后台线程执行耗时操作
DispatchQueue.global().async {
    let result = self.performHeavyTask()
    
    // 主线程更新UI
    DispatchQueue.main.async {
        self.updateUI(with: result)
    }
}
```

### 13.4 协议和扩展

优先使用协议和扩展而非继承：

```swift
protocol Loadable {
    func showLoadingView()
    func hideLoadingView()
}

extension Loadable where Self: UIViewController {
    func showLoadingView() {
        // 默认实现
    }
}
```

## 十四、Swift和Objective-C混编规范

### 14.1 新功能开发

- 所有新功能模块应使用Swift开发
- 如需使用现有Objective-C代码，应通过桥接方式调用

### 14.2 桥接文件管理

- 在桥接头文件(`AICoin-Bridging-Header.h`)中只导入必要的Objective-C头文件
- Swift代码暴露给Objective-C时，使用`@objc`标记

```swift
@objc class AICTickerManager: NSObject {
    @objc static let shared = AICTickerManager()
    
    @objc func fetchTickerData(completion: @escaping ([String: Any]?, Error?) -> Void) {
        // 实现代码
    }
}
```

### 14.3 类型转换

- 在Swift和Objective-C之间传递数据时，注意类型映射关系
- 使用`as?`安全转换类型，避免强制转换

```swift
// Swift中调用Objective-C方法
if let result = objcManager.fetchData() as? [String: Any] {
    // 处理结果
}
```

## 十五、常见问题解决方案

### 15.1 主题切换问题

**问题**：主题切换后UI未更新

**解决方案**：

```swift
// 监听主题切换通知
NotificationCenter.default.addObserver(self, selector: #selector(applyThemeChange), name: .SSBThemeManagerShouldChangeTheme, object: nil)

// 应用主题变化
@objc func applyThemeChange() {
    view.backgroundColor = UIColor.base.current.backgroundColor
    titleLabel.textColor = UIColor.base.current.titleTextColor
    iconImageView.image = UIImage.base.image(name: "icon_name")
}
```

### 15.2 内存泄漏问题

**解决方案**：

```swift
// 使用weak声明delegate
weak var delegate: MyProtocol?

// 使用DisposeBag管理订阅
private let disposeBag = DisposeBag()

viewModel.marketModels.subscribe { [weak self] models in
    self?.tableView.reloadData()
}.disposed(by: disposeBag)
```

### 15.3 网络请求问题

**解决方案**：

```swift
// 处理网络请求错误
AICBaseHttpManager.shared.post("/api/v1/endpoint", parameters: params, progress: nil, success: { (task, response) in
    // 处理成功响应
}, failure: { (task, error) in
    if let error = error as NSError? {
        if error.domain == NSURLErrorDomain {
            self.showNetworkErrorAlert()
        } else {
            self.showErrorMessage(error.localizedDescription)
        }
    }
})
```

## 十六、代码审查清单

在提交代码前，请检查以下事项：

- [ ] 代码遵循命名规范
- [ ] 没有硬编码的字符串（已本地化）
- [ ] 没有内存泄漏（检查循环引用）
- [ ] 适当的错误处理
- [ ] 支持深色模式
- [ ] 代码有适当的注释
- [ ] 没有编译警告
- [ ] 代码格式一致
- [ ] 使用了推荐的组件和架构
- [ ] 网络请求正确处理错误情况
- [ ] UI适配不同设备尺寸

通过遵循本规范，可以提高AICoin-iOS项目的代码质量、性能和可维护性，为用户提供更好的使用体验。 